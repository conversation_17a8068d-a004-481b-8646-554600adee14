
/******************** (C) COPYRIGHT 2022 BSM ********************
* File Name          : app_ad.c
* Author             : 
* Version            : V1.0
* Date               : 
* Description        : 
********************************************************************************/
#include <rtthread.h>
#include <string.h>
#include <math.h>
#include "app_ad.h"
#include "uApp.h"
#include "alg_ntc_convert.h"
#include "alg_toolbox.h"

// ADC conversion optimization constants - use shift operations to improve efficiency
// Principle: x/4096 = x>>12 (because 4096 = 2^12)
// For x*k/4096, can be rewritten as (x*k)>>12

// Inline function definitions, compiler will optimize to efficient shift operations
static inline float adc_to_3v3(uint16_t adc_val) {
    // Original: adc_val * 3.3f / 4096.0f
    // Optimized: adc_val * 3.3f * (1.0f/4096.0f) = adc_val * 0.0008056640625f
    return (float)adc_val * 0.0008056640625f;  // 3.3/4096 pre-calculated
}

static inline float adc_to_3300mv(uint16_t adc_val) {
    // Original: adc_val * 3300.0f / 4096.0f
    // Optimized: adc_val * 0.8056640625f
    return (float)adc_val * 0.8056640625f;     // 3300/4096 pre-calculated
}

static inline float adc_to_23v3(uint16_t adc_val) {
    // Original: adc_val * 23.3f / 4096.0f
    // Optimized: adc_val * 0.0056884765625f
    return (float)adc_val * 0.0056884765625f;  // 23.3/4096 pre-calculated
}

static inline float adc_to_8v25(uint16_t adc_val) {
    // Original: adc_val * 8.25f / 4096.0f
    // Optimized: adc_val * 0.00201416015625f
    return (float)adc_val * 0.00201416015625f; // 8.25/4096 pre-calculated
}

// Test function: verify the precision of ADC conversion optimization
// Can be called during debugging to verify precision
static void test_adc_conversion_precision(void)
{
    // Test different ADC values
    uint16_t test_values[] = {0, 1, 100, 1000, 2048, 4095};
    int test_count = sizeof(test_values) / sizeof(test_values[0]);

    for(int i = 0; i < test_count; i++)
    {
        uint16_t adc_val = test_values[i];

        // Test 3.3V conversion
        float original_3v3 = (float)adc_val * 3.3f / 4096.0f;
        float optimized_3v3 = adc_to_3v3(adc_val);
        float error_3v3 = optimized_3v3 - original_3v3;

        // Test 3300mV conversion
        float original_3300mv = (float)adc_val * 3300.0f / 4096.0f;
        float optimized_3300mv = adc_to_3300mv(adc_val);
        float error_3300mv = optimized_3300mv - original_3300mv;

        // Test 23.3V conversion
        float original_23v3 = (float)adc_val * 23.3f / 4096.0f;
        float optimized_23v3 = adc_to_23v3(adc_val);
        float error_23v3 = optimized_23v3 - original_23v3;

        // Test 8.25V conversion
        float original_8v25 = (float)adc_val * 8.25f / 4096.0f;
        float optimized_8v25 = adc_to_8v25(adc_val);
        float error_8v25 = optimized_8v25 - original_8v25;

        // These values can be observed in the debugger
        // printf("ADC: %u, 3.3V err: %f, 3300mV err: %f, 23.3V err: %f, 8.25V err: %f\n",
        //        adc_val, error_3v3, error_3300mv, error_23v3, error_8v25);
    }
}

float ntc_get_resis(uint16_t ad)
{
    float _v,_rdn,_ntc;
    // V_measured = V_supply * R224 / (R_ntc + R224)
    // Solve for R_ntc:
    // R_ntc = R224 * (V_supply - V_measured) / V_measured
    _v = adc_to_3v3(ad);  // Use the optimized ADC conversion function
    _rdn = 0.39f;
    _ntc = (5-_v)/(_v/_rdn);

    return _ntc;
}

//---------------------------------------------------------------------
// Raw AD sampling values
//---------------------------------------------------------------------
volatile  rt_uint16_t ADC1_ValTemp[ADC1_CHANNELS] = {0};
volatile  rt_uint16_t ADC2_ValTemp[ADC2_CHANNELS] = {0};
volatile  rt_uint16_t ADC3_ValTemp[ADC3_CHANNELS] = {0};
volatile  rt_uint16_t ADC4_ValTemp[ADC4_CHANNELS] = {0};
volatile  rt_uint16_t ADC5_ValTemp[ADC5_CHANNELS] = {0};
void acOutput_adc_collect(void);
CurExcursion_t gIoutExcursion;

extern ADC_HandleTypeDef hadc1;
extern ADC_HandleTypeDef hadc2;
extern ADC_HandleTypeDef hadc3;
extern DMA_HandleTypeDef hdma_adc1;
extern DMA_HandleTypeDef hdma_adc2;
extern DMA_HandleTypeDef hdma_adc3;

static __IO uint32_t enter_tick = 0;
static __IO uint32_t prev_tick = 0;

/**
  * @brief  ADC3
            VAC_U        PB0         ADC3_IN12/ADC1_IN15
            VAC_V        PB1         ADC3_IN1/ADC1_IN12
            POW_DIODE_T  PE7         ADC3_IN4
            24V_MCU      PB13        ADC3_IN5
  * @param 
  * @retval 
  */
void DMA2_Channel4_IRQHandler(void)
{
    rt_ubase_t  level;

    level = rt_hw_interrupt_disable();
    
  /* USER CODE BEGIN DMA1_Channel1_IRQn 0 */
    HAL_DMA_IRQHandler(&hdma_adc3);
    
    if(__HAL_DMA_GET_IT_SOURCE(hadc3.DMA_Handle, DMA_IT_TC) != RESET)
    {     
        enter_tick = TIM3->CNT;
        vfd.use_tick  = (enter_tick > prev_tick) ? (enter_tick - prev_tick) : vfd.use_tick;
        prev_tick = enter_tick;
    
        vfd.fast_ad.ac_vout_u = (ADC3_ValTemp[0] * 1421.635f/4096.0f-710.8178f);
        vfd.fast_ad.ac_vout_v = (ADC3_ValTemp[1] * 1421.635f/4096.0f-710.8178f);
        vfd.fast_ad.dc_24V   = ADC3_ValTemp[3] * 36.3f /4096.0f;
        
    }
    rt_hw_interrupt_enable(level);
    
    
  /* USER CODE END DMA1_Channel1_IRQn 0 */
}

/**
  * @brief  ADC2 
            VAC_W       PB2        ADC2_IN12
            AUX1_MCU    PA6        ADC2_IN3
            AUX2_MCU    PA7        ADC2_IN4
            EMC_LIT     PC4        ADC2_IN5
            PFC_LIT     PC5        ADC2_IN11
            5V_MCU      PB15       ADC2_IN15
  * @param 
  * @retval 
  */
void DMA2_Channel3_IRQHandler(void)
{
    rt_ubase_t  level;

    level = rt_hw_interrupt_disable();
    
  /* USER CODE BEGIN DMA1_Channel1_IRQn 0 */
    HAL_DMA_IRQHandler(&hdma_adc2);
    
    if(__HAL_DMA_GET_IT_SOURCE(hadc3.DMA_Handle, DMA_IT_TC) != RESET)
    {     
        enter_tick = TIM3->CNT;
        vfd.use_tick  = (enter_tick > prev_tick) ? (enter_tick - prev_tick) : vfd.use_tick;
        prev_tick = enter_tick;
    
        vfd.fast_ad.ac_vout_w = (ADC2_ValTemp[0] * 1421.635f/4096.0f-710.8178f);
        vfd.fast_ad.aux1  = ADC2_ValTemp[1] * 26.4f /4096.0f; // 4-20mA
        vfd.fast_ad.aux2  = ADC2_ValTemp[2] * 13.7f /4096.0f; // 0-10V

        vfd.fast_ad.dc_5V   = ADC2_ValTemp[5] * 8.25f /4096.0f;
        
    }
    rt_hw_interrupt_enable(level);
    
    
  /* USER CODE END DMA1_Channel1_IRQn 0 */
}

extern uint16_t pfc_adc_value[8];
AC_Phase_T acvin_r,acvin_s,acvin_t;
AC_Phase_T aciin_r,aciin_s,aciin_t;
AC_Phase_T acvout_u,acvout_v,acvout_w;
void acin_transform2rms(void)
{
    uint16_t period_cnt = 200; // 10k / 50hz

    // Use the optimized ADC conversion function to avoid division operations
    toolbox_rms_get(&acvin_r,DPLL3P.VLac.In.a,adc_to_3v3(pfc_adc_value[0]),period_cnt);
    toolbox_rms_get(&acvin_s,DPLL3P.VLac.In.b,adc_to_3v3(pfc_adc_value[1]),period_cnt);
    toolbox_rms_get(&acvin_t,DPLL3P.VLac.In.c,adc_to_3v3(pfc_adc_value[2]),period_cnt);

    toolbox_rms_get(&aciin_r,DPLL3P.Iac.In.a,adc_to_3v3(pfc_adc_value[4]),period_cnt);
    toolbox_rms_get(&aciin_s,DPLL3P.Iac.In.b,adc_to_3v3(pfc_adc_value[5]),period_cnt);
    toolbox_rms_get(&aciin_t,DPLL3P.Iac.In.c,adc_to_3v3(pfc_adc_value[6]),period_cnt);
    
    
    vfd.fast_ad.ac_vin_r = acvin_r._rms;
    vfd.fast_ad.ac_vin_s = acvin_s._rms;
    vfd.fast_ad.ac_vin_t = acvin_t._rms;
    
    vfd.fast_ad.ac_iin_r = aciin_r._rms;
    vfd.fast_ad.ac_iin_s = aciin_s._rms;
    vfd.fast_ad.ac_iin_t = aciin_t._rms;
}




/*******************************************************************************
* Function Name  : AD_Interface_DataInteract  
* Description    : Exchange data with user interface  
*                   
*
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void AD_Interface_DataInteract(void)
{
    
    uint32_t start,end;

    static float v15 = 0;
    static float v5 = 0;
    static float dcIin = 0;
    static uint32_t  cnt = 0;
    
    // 1ms cycle
    cnt++;
    
    if(cnt >= 2000)
        vfd.bit.ad_init = 1;
    
    
    vfd.abs_fast_acin_freq = vfd.pfc->AbsFreq;
    vfd.acin_freq     = F32_FoLowPassFilter(vfd.acin_freq,vfd.pfc->Freq,0.25);
    vfd.abs_acin_freq = fabs(vfd.acin_freq);
    
    // divided-frequency for 10 ms
    if(cnt % 10 != 0)
    {
        return;
    }
    
    start = rt_tick_get();
    
    /*  get NTC/PT100 temp */
    vfd.fast_ad.temp_pfc_mos   = ntc_convert_3470_5k(ntc_get_resis(ADC3_ValTemp[4]))/10;
    vfd.fast_ad.temp_inv_mos   = ntc_convert_3470_5k(ntc_get_resis(ADC4_ValTemp[10]))/10;
    vfd.fast_ad.temp_POW_DIODE = ntc_convert_3470_5k(ntc_get_resis(ADC3_ValTemp[2]))/10;
    vfd.fast_ad.temp_EMC_L     = ntc_convert_3470_5k(ntc_get_resis(ADC2_ValTemp[3]))/10;
    vfd.fast_ad.temp_PFC_L     = ntc_convert_3470_5k(ntc_get_resis(ADC2_ValTemp[4]))/10;
    vfd.fast_ad.temp_BUS_CAP2  = ntc_convert_3470_5k(ntc_get_resis(ADC2_ValTemp[6]))/10;
    vfd.fast_ad.temp_BUS_CAP   = ntc_convert_3470_5k(ntc_get_resis(ADC5_ValTemp[9]))/10;

    /*  get CPU temp */
    {
        uint16_t TS_CAL1 = *(uint16_t*)0x1FFF75A8;
        uint16_t TS_CAL2 = *(uint16_t*)0x1FFF75CA;
    
//        vfd.fast_ad.temp_mcu = (110-30)*(ADC1_ValTemp[4]*3.32f/3.0f-TS_CAL1)/(TS_CAL2-TS_CAL1) + 30;	
    }
    
    vfd.fast_ad.temp_hdc1080 = hdc1080_get_temperature();
    vfd.fast_ad.mosi_hdc1080 = hdc1080_get_moisture();

    // Use the optimized ADC conversion function to avoid division operations
    vfd.fast_ad.ac_vout     = vfd.inverter->OutVoltage;
    
    vfd.filter_ad.ac_vin_r      = F32_FoLPFilter(0.01f,  10.0f,  vfd.filter_ad.ac_vin_r, acvin_r._rms*(1+vfd.SetAdCa.adca[0]*0.001));
    vfd.filter_ad.ac_vin_s      = F32_FoLPFilter(0.01f,  10.0f,  vfd.filter_ad.ac_vin_s, acvin_s._rms*(1+vfd.SetAdCa.adca[1]*0.001));
    vfd.filter_ad.ac_vin_t      = F32_FoLPFilter(0.01f,  10.0f,  vfd.filter_ad.ac_vin_t, acvin_t._rms*(1+vfd.SetAdCa.adca[2]*0.001));
                                                        
    vfd.filter_ad.ac_iin_r      = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iin_r, aciin_r._rms*(1+vfd.SetAdCa.adca[3]*0.001)) ;
    vfd.filter_ad.ac_iin_s      = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iin_s, aciin_s._rms*(1+vfd.SetAdCa.adca[4]*0.001));
    vfd.filter_ad.ac_iin_t      = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iin_t, aciin_t._rms*(1+vfd.SetAdCa.adca[5]*0.001));
                                                        
    vfd.filter_ad.ac_iout_u     = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iout_u, vfd.fast_ad.ac_iout_u*(1+vfd.SetAdCa.adca[8]*0.001));
    vfd.filter_ad.ac_iout_v     = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iout_v, vfd.fast_ad.ac_iout_v*(1+vfd.SetAdCa.adca[9]*0.001));
    vfd.filter_ad.ac_iout_w     = F32_FoLPFilter(0.01f,  1.0f,   vfd.filter_ad.ac_iout_w, vfd.fast_ad.ac_iout_w*(1+vfd.SetAdCa.adca[10]*0.001));
    vfd.filter_ad.ac_vout_u     = F32_FoLPFilter(0.01f,  10.0f,   vfd.filter_ad.ac_vout_u, acvout_u._rms*(1+vfd.SetAdCa.adca[11]*0.001));
    vfd.filter_ad.ac_vout_v     = F32_FoLPFilter(0.01f,  10.0f,   vfd.filter_ad.ac_vout_v, acvout_v._rms*(1+vfd.SetAdCa.adca[12]*0.001));
    vfd.filter_ad.ac_vout_w     = F32_FoLPFilter(0.01f,  10.0f,   vfd.filter_ad.ac_vout_w, acvout_w._rms*(1+vfd.SetAdCa.adca[13]*0.001));


    vfd.filter_ad.dc_12V        = F32_FoLPFilter(0.01f,   1.0f,   vfd.filter_ad.dc_12V, vfd.fast_ad.dc_12V);
    vfd.filter_ad.dc_5V         = F32_FoLPFilter(0.01f,   1.0f,   vfd.filter_ad.dc_5V,vfd.fast_ad.dc_5V);
    vfd.filter_ad.vbus_inv      = F32_FoLPFilter(0.01f,  25.0f,   vfd.filter_ad.vbus_inv, vfd.fast_ad.vbus_inv);
                                                     
    vfd.filter_ad.temp_PFC_L    = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_PFC_L  ,vfd.fast_ad.temp_PFC_L  );
    vfd.filter_ad.temp_pfc_mos  = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_pfc_mos,vfd.fast_ad.temp_pfc_mos);
    vfd.filter_ad.temp_inv_mos  = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_inv_mos,vfd.fast_ad.temp_inv_mos);
    vfd.filter_ad.temp_BUS_CAP  = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_BUS_CAP,vfd.fast_ad.temp_BUS_CAP);
    vfd.filter_ad.temp_POW_DIODE = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_POW_DIODE,vfd.fast_ad.temp_POW_DIODE);
    vfd.filter_ad.temp_EMC_L    = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_EMC_L,vfd.fast_ad.temp_EMC_L);
    vfd.filter_ad.temp_BUS_CAP2 = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_BUS_CAP2,vfd.fast_ad.temp_BUS_CAP2);

    vfd.filter_ad.temp_hdc1080  = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.temp_hdc1080,vfd.fast_ad.temp_hdc1080);
    vfd.filter_ad.mosi_hdc1080  = F32_FoLPFilter(0.005f, 10.0f,   vfd.filter_ad.mosi_hdc1080,vfd.fast_ad.mosi_hdc1080);
    vfd.filter_ad.aux1          = F32_FoLPFilter(0.2f,  10.0f,  vfd.filter_ad.aux1,vfd.fast_ad.aux1); 
    vfd.filter_ad.aux2          = F32_FoLPFilter(0.2f,  10.0f,  vfd.filter_ad.aux2,vfd.fast_ad.aux2);     
    vfd.filter_ad.ac_vout       = F32_FoLPFilter(0.2f,  10.0f,  vfd.filter_ad.ac_vout,vfd.fast_ad.ac_vout);
    
    end = rt_tick_get();
    end = end - start;
    
}

/*******************************************************************************
* Function Name  : task_AD 
* Description    : System bottom AD data processing task.   
*                   
*
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void adc_sample_task(void)
{
	//--------------------------------------------------------------
	// Data exchange
	//--------------------------------------------------------------
	AD_Interface_DataInteract();
}


#define ADCA_MAX_VALUE      1500  // 1.500
#define ADCA_MIN_VALUE       500  // 0.500
#define ADCA_DEFAULT_VALUE  1000  // 1.000
int adca_data_fix(uint16_t *data)
{
    int  result = 0;

    for (int i = 0; i < (sizeof(vfd.SetAdCa.adca) / 2); i++)
    {
        if ((data[i] > ADCA_MAX_VALUE) || (data[i] < ADCA_MIN_VALUE))
        {
            data[i] =  ADCA_DEFAULT_VALUE;
            result = 1;
        }
    }

    return result;
}


/*******************************************************************************
* Function Name  : void ad(void) 
* Description    :    
*                   
*
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/

void cmd_ad(int argc, char **argv)
{	
    if(argc == 1)
	{
        rt_kprintf("\r\n");
	rt_kprintf("\r\n >>> from VFD >>>\r\n");

    
    rt_kprintf("\r\n");
    }
    else if(!rt_strcmp("adc1",argv[1]))
    {
        int i = atoi(argv[2]);
        
        rt_kprintf(" %3d : ADC1_ValTemp[%d]\r\n",ADC1_ValTemp[i],i);
    }
    else if(!rt_strcmp("adc2",argv[1]))
    {
        int i = atoi(argv[2]);
        
        rt_kprintf(" %3d : ADC2_ValTemp[%d]\r\n",ADC2_ValTemp[i],i);
    }
    else if(!rt_strcmp("adc3",argv[1]))
    {
        int i = atoi(argv[2]);
        
        rt_kprintf(" %3d : ADC3_ValTemp[%d]\r\n",ADC3_ValTemp[i],i);
    }
    else if(!rt_strcmp("adc4",argv[1]))
    {
        int i = atoi(argv[2]);
        
        rt_kprintf(" %3d : ADC4_ValTemp[%d]\r\n",ADC4_ValTemp[i],i);
    }
    else if(!rt_strcmp("adc5",argv[1]))
    {
        int i = atoi(argv[2]);
        
        rt_kprintf(" %3d : ADC5_ValTemp[%d]\r\n",ADC5_ValTemp[i],i);
    }
}


//---------------------------------------------------------------------
// Console command function hook
//---------------------------------------------------------------------
#include <finsh.h>
MSH_CMD_EXPORT_ALIAS(cmd_ad,ad, Show the ad value)
/******************* (C) COPYRIGHT 2013 Group *****END OF FILE****/
