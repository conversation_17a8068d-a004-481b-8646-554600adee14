
#ifndef __CUSTOMER_PORT_H__
#define __CUSTOMER_PORT_H__


#if defined(CUSTOMER_MUTEX) 
#error "customer more than 1!"
#endif

#define CUSTOMER_MUTEX

/* king */
#define RS485_ACK_DELAY             5
#define RS485_DE_DELAY              5
#define RS485_TIMEOUT_DELAY         5
#define VFD_SLOWDOWN_STOP_ENABLE    0

#define AC_VIN_OVER             463 // ??????463V??1S
#define AC_VIN_OVER_RESUME      453 // ???????-10V (453V)

#define AC_VIN_LOW              325 // ??????325V??2S
#define AC_VIN_LOW_RESUME       335 // ???????+10V (335V)
  

#define AC_IIN_OVER1            117.2f  // 规范要求：变频器额定电流97.67A，120%过载保护    
#define AC_IIN_OVER2            195.34f // 规范要求：变频器额定电流97.67A，200%严重过载保护
#define AC_IIN_RESUME           97.67f  // 规范要求：变频器额定电流97.67A
#define AC_IIN_PID_REF          97.67f  // 规范要求：变频器额定电流97.67A

#define AC_IOUT_RESUME          97.67f  // 规范要求：电机额定电流97.67A
#define AC_IOUT_PID_REF         97.67f  // 规范要求：电机额定电流97.67A

#define IOUT_OVERLOAD_BASE      40000
#define IIN_OVERLOAD_BASE       40000

#endif



 
#define DC_VIN_OVER             148
#define DC_VIN_RESUME           143
    
#define DC_VIN_LOW              67
#define DC_VIN_LOW_RESUME       72
    
#define DC_VIN_VALID            50    
#define AC_VIN_VALID            250    
    
#define VBUS_OVER1              720 // ??????720V??100ms
#define VBUS_OVER2              750 // ??????750V??100ms
#define VBUS_RESUME             715 // ???????-5V (715V)

#define VBUS_LOW                250 // ??????250V??5s
#define VBUS_LOW_RESUME         300 // ???????+5V (255V)

#define DC15V_OVER              18
#define DC15V_OVER_RESUME       17
    
#define DC15V_LOW               12
#define DC15V_LOW_RESUME        13

#define DC5V_OVER               5.5
#define DC5V_OVER_RESUME        5.3
    
#define DC5V_LOW                4.5
#define DC5V_LOW_RESUME         4.7

#define ACIN_FREQ_LOW           47
#define ACIN_FREQ_LOW_RESUME    47.5

#define ACIN_FREQ_OVER          63
#define ACIN_FREQ_OVER_RESUME   62.5

#define ACIOUT_LOSEPHASE_CURR       1.0 /** 1.0A*/


