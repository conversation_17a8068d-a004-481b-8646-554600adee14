
#ifndef __CUSTOMER_PORT_H__
#define __CUSTOMER_PORT_H__


#if defined(CUSTOMER_MUTEX) 
#error "customer more than 1!"
#endif

#define CUSTOMER_MUTEX

/* king */
#define RS485_ACK_DELAY             5
#define RS485_DE_DELAY              5
#define RS485_TIMEOUT_DELAY         5
#define VFD_SLOWDOWN_STOP_ENABLE    0

#define AC_VIN_OVER             455 //475
#define AC_VIN_OVER_RESUME      445 //465
    
#define AC_VIN_LOW              328
#define AC_VIN_LOW_RESUME       338
  

#define AC_IIN_OVER1            5.83f   //3200VA *1.2 /380V/1.732    ÒÑÆÁ±Î
#define AC_IIN_OVER2            7.0f    //3200VA *1.2 /320V/1.732
#define AC_IIN_RESUME           4.86f   //3200VA *1.0 /380V/1.732
#define AC_IIN_PID_REF          5.0f    //3290VA /380V/1.732

#define AC_IOUT_RESUME          4.86f   //3200VA *1.0 /380V/1.732
#define AC_IOUT_PID_REF         6.0f    //3950VA /380V/1.732 
    
#define DC_IIN_OVER1            35.0f
#define DC_IIN_OVER2            43.6f
#define DC_IIN_RESUME           29.0f
#define DC_IIN_PID_REF          17.0f

#define IOUT_OVERLOAD_BASE      1300
#define IIN_OVERLOAD_BASE       1300

#endif



 
#define DC_VIN_OVER             148
#define DC_VIN_RESUME           143
    
#define DC_VIN_LOW              67
#define DC_VIN_LOW_RESUME       72
    
#define DC_VIN_VALID            50    
#define AC_VIN_VALID            250    
    
#define VBUS_OVER1              750
#define VBUS_OVER2              800
#define VBUS_RESUME             715
    
#define VBUS_LOW                460
#define VBUS_LOW_RESUME         470

#define DC15V_OVER              18
#define DC15V_OVER_RESUME       17
    
#define DC15V_LOW               12
#define DC15V_LOW_RESUME        13

#define DC5V_OVER               5.5
#define DC5V_OVER_RESUME        5.3
    
#define DC5V_LOW                4.5
#define DC5V_LOW_RESUME         4.7

#define ACIN_FREQ_LOW           47
#define ACIN_FREQ_LOW_RESUME    47.5

#define ACIN_FREQ_OVER          63
#define ACIN_FREQ_OVER_RESUME   62.5

#define ACIOUT_LOSEPHASE_CURR       1.0 /** 1.0A*/


