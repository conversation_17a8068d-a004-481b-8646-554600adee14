# PVPB09空压机变频器

名称：PVPB09空压机变频器功能规范说明书编号：BSM.PVPB09- SW- 000版本：V1.0

版权专有，违者必究深圳市波斯曼技术有限公司

Shenzhen Bus- lan Technology Co.,Ltd.

BSM.PVPB09- SW- 000

# 修改历史

<table><tr><td>版本</td><td>通知单号</td><td>修改日期</td><td>修改者</td><td>修改说明</td></tr><tr><td>V1.00</td><td>——</td><td>2025-07-22</td><td>欧鹏逸</td><td>新生文件
(共21页)</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table>

# 目录

1.1简介.

1.2概述.

1.3变频器设置&电机类型

# 1.4 MCU外设配置

1.5启机逻辑（）

1.6散热风扇工作逻辑（）

1.7母线主继电器吸合逻辑（）

1.8PFC逐波限流逻辑（）

1.9INV逐波限流逻辑（）

1.10通讯接口逻辑（）

1.11LED指示灯逻辑（）

1.12输入输出口定义

1.12.1DI输入定义（）

1.12.2空压机地址定义（）

1.12.3DO输出定义（）

1.13故障触发逻辑

1.13.1交流输入过载保护（）

1.13.2输出交流过载保护（）

1.13.3交流过欠压故障（）

1.13.4母线过欠压故障（）

1.13.5过温故障（）

1.13.6温度传感器故障（）

# 1.13.7 三相交流输入缺相故障 () 9

1.13.8 5V 电源故障 () 91.13.9 12V 电源故障 () 101.13.10 24V 电源故障 () 101.13.11 输入交流电压采样传感器故障 () 101.13.12 输出交流电压采样传感器故障 () 101.13.13 母线电压传感器故障 () 111.13.14 输入交流电流采样传感器故障 () 111.13.15 输出交流电流采样传感器故障 () 111.13.16 输入硬件过流故障 () 111.13.17 输出硬件过流故障 () 111.13.18 母线硬件过压故障 () 111.13.19 母线主电缆电器故障 () 121.13.20 PFC 驱动短路故障 () 121.13.21 逆变输出短路故障 () 121.13.22 PFC 电感温度开关过温故障 () 121.13.23 控制电源掉电故障 () 121.13.24 参考源故障 () 131.13.25 三相交流输出缺相故障 () 131.13.26 输出接地故障 () 131.13.27 主电源接错故障 () 131.13.28 输入无压 () 131.13.29 三相交流输入相序错误 () 14

1.13.30 输入频率低&输入频率高 () 141.13.31 MCU 外设故障 () 141.13.32 通讯故障 () 141.14 其他信息 141.14.1 系统载波 () 141.15 系统 AD 采样确认 151.16 故障记录与日志记录 151.17 大数据信息内容 151.18 AD 校准功能 171.19 软件特殊要求功能 171.20 上位机要求 171.21 参数拷贝 181.22 故障继电器输出条件 18

# 1.1 简介

# 1.2 概述

1.2 概述系统主控为STM32G4系列MCU分别运算三相六开关PFC与电机控制、系统逻辑处理及对外接口。系统对外主要接口有，数字DI、数字DO、模拟量输入（交流输入电压、交流输入电流、母线电压、交流输出电压、交流输出电流、温度采集），可适配RS- 485与CAN。电机类型可PMSM永磁同步电机或异步电机。PC端上位机可通过USB转RS485或USB转CAN连接变频器，首选RS- 485接口，可通过RS- 485或者CAN通讯接口对系统MCU进行程序升级。变频器额定交流输入380V或DC600V,额定输出功率40kW，最大功率47kW。

# 1.3 变频器设置&电机类型

1.3 变频器设置&电机类型A：变频器设置机型20，变频器额定功率45kW，额定有效电流99.3A；B：支持永磁同步电机与异步电机；C：同步电机支持电机参数学习，异步电机支持低频扭矩补偿；

# 1.4 MCU外设配置

以下DI输入MCU做同相向上下拉处理，DO做反向上下拉处理

1.4 MCU外设配置以下DI输入MCU做同相向上下拉处理，DO做反向上下拉处理A：输入交流R相电压采样（PDB：kVAC_R_MCU）交流采样；（相位正向）0V对应峰值- 862.54V,有效值- 610V，1.65V对应0V,3.3V对应峰值+862.54V,有效值+610V；B：输入交流S相电压采样（PD9：kVAC_S_MCU）交流采样；（相位正向）0V对应峰值- 862.54V,有效值- 610V，1.65V对应0V,3.3V对应峰值+862.54V,有效值+610V；C：输入交流T相电压采样（PD10：kVAC_S_MCU）交流采样；（相位正向）0V对应峰值- 862.54V,有效值- 610V，1.65V对应0V,3.3V对应峰值+862.54V,有效值+610V；BSM.PVPB09- SW- 000第1页共13页深圳市波斯曼技术有限公司

D：输入交流R相电流采样（PD11：kVAC_R_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A；

E：输入交流S相电流采样（PD12：kVAC_S_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A;

F：输入交流T相电流采样（PD13：kVAC_S_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A;

G：母线电压采样（PC0：VBUS_MCU）直流采样；

3.3V对应995V；

H：输出交流U相电压采样（PB0：kVAC_R_MCU）交流采样；(相位正向)

0V对应峰值- 710.82V,有效值- 502.7V，1.65V对应0V,3.3V对应峰值+710.82V,有效值+502.7V;

I：输出交流V相电压采样（PB1：kVAC_S_MCU）交流采样；(相位正向)

0V对应峰值- 710.82V,有效值- 502.7V，1.65V对应0V,3.3V对应峰值+710.82V,有效值+502.7V;

J：输出交流W相电压采样（PB2：kVAC_S_MCU）交流采样；(相位正向)

0V对应峰值- 710.82V,有效值- 502.7V，1.65V对应0V,3.3V对应峰值+710.82V,有效值+502.7V;

K：输出交流U相电流采样（PC3：I_U_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A;

L：输出交流V相电流采样（PC2：I_V_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A;

M：输出交流W相电流采样（PC1：I_W_MCU）交流采样；(相位正向)

0V对应峰值- 280.82A,有效值- 198.6A，1.65V对应0A,3.3V对应峰值+280.82A,有效值+198.6A;

N：共模电感温度采样（PC4：EMC_L1T）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

BSM.PVPB09- SW- 000 第2页共13页深圳市波斯曼技术有限公司

O：PFC电感温度采样（PC5：PFC_L1T）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

P：母线电容1温度采样（PAB：CAP1- T- MCU）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

Q：母线电容2温度采样（PB11：CAP2- T- MCU）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

R：PFC模块温度采样（PD14：PFC_MOS_TT）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

S：INV逆变模块温度采样（PB12：INV_MOS_T）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

T：辅助电源整流二极管温度采样（PE7：POW_DIODE_T）直流采样；

B25/50=3470K,R25=5K,参照PVPB02PFC电感温度采样；

U：0- 10V外部模拟量输入采样（PA7：AUX2_MCU）直流采样；

3.3. V对应13.7V；

V：4- 20mA外部模拟量输入采样（PA6：AUX1_MCU）直流采样；

3.3. V对应26.4mA；

W：5V电压采样（PB15：5V_MCU）直流采样；

3.3V对应8.25V；

X：12V电压采样（PB14：12V_MCU）直流采样；

3.3V对应23.3V；

Y：24V电压采样（PB13：24V_MCU）直流采样；

3.3V对应36.3V；

BSM.PVPB09- SW- 000

# Z：参考源检测（VREF）直流采样；

# 3.3V对应3.3V；

A1：模拟输出电压1（PA4：DA1_F）模拟量输出；

3.3V对应xxx（待定）；

B1：模拟输出电压2（PA5：DA2_F）模拟量输出；

3.3V对应xxx（待定）；

C1：PFC逐波限流信号（PD2：PFC_CBC）；

低电平CBC，高电平正常，脉冲信号；

D1：INV逐波限流信号（PE15：INV_CBC）；

低电平CBC，高电平正常，脉冲信号；

E1：PFC短路保护信号（PD1：F- HD_MCU1）中断信号，与INV同一个信号；

低电平故障，高电平正常；

F1：INV短路保护信号（PE14：F- HD_MCU1）中断信号，与PFC同一个信号；

低电平故障，高电平正常；

G1：三相输入电流硬件保护信号（PF8：I- HD_OC）中断信号；

低电平故障，高电平正常，脉冲信号；

H1：母线过压保护信号（PF14：OVP_P_BUS）中断信号；

低电平故障，高电平正常，脉冲信号；

I1：三相输出电流硬件保护信号（PF9：O- HD_OC）中断信号；

低电平故障，高电平正常，脉冲信号；

J1：主继电器吸合故障信号（PF6：KMON_FLT）中断信号；

低电平故障，高电平正常，脉冲信号；

K1：PFC驱动短路保护信号（PF10：F- IPM- PFC_MCU1）中断信号；

低电平故障，高电平正常，脉冲信号；

L1：INV驱动短路保护信号（PF11：F- IPM- INV_MCU1）中断信号；

低电平故障，高电平正常，脉冲信号；

M1：110V掉电检测信号（PF11：POW）中断信号；

低电平掉电，高电平正常，脉冲信号；

N1：母线主继电器控制（PA0：KMON1）；

低电平断开，高电平吸合，PWM信号；

O1：风扇PWM控制（PF7：PWM_FAN）；

低电平待机，高电平开启，PWM信号；

P1：地址码1（PE0：Add1）DI输入；

低电无效，高电有效；

Q1：地址码2（PE1：Add2）DI输入；

低电无效，高电有效；

R1：数字输入1（PE2：IN1）DI输入；

低电无效，高电有效；

S1：数字输入2（PE3：IN2）DI输入；

低电无效，高电有效；

T1：数字输入3（PE4：IN3）DI输入；

低电无效，高电有效；

U1：数字输入4（PE5：IN4）DI输入；

低电无效，高电有效；

BSM.PVPB09- SW- 000

V1：PFC电感温度开关（PE6：TPSW）DI输入；

低电无效，高电有效；

W1：数字输出1控制（PG0：O1）DOo输出；

低电平断开，高电平吸合；

X1：数字输出2控制（PG1：O2）DO输出；

低电平断开，高电平吸合；

Y1：数字输出3控制（PG2：O3）DO输出；

低电平断开，高电平吸合；

Z1：硬件故障（PG5：F- HD_MCU1）DI输入；

低电平故障，高电平正常；

A2：检测FS输出使能（PG7：FS_DR）DI输入；

低电平工作，高电平待机或故障；

B2：驱动输出使能（PG6：FS_DR）DO输出；

低电平使能，高电平待机；

C2：硬件故障，软件清除（PG8：CLR-HD）DO输出；

低电平清除，高电平正常；

D2：运行灯（PD4：RUN）DO输出；

低电平亮，高电平灭；

E2：故障灯（PD7：FLT）DO输出；

低电平亮，高电平灭；

F2：RS- 485通讯灯（PD5：485- COM）DO输出；

# 低电平亮，高电平灭；

G2：CAN通讯灯（PD6：CAN- COM）DO输出；

# 低电平亮，高电平灭；

H2：32MFLASH（PC9,PC10,PC11,PC12）SPI口；

I2：TIM8（PB3, PB4, PB5, PC6, PC7, PC8）三相PFC控制PWM；

J2：TIM1（PE8, PE9, PE10, PE11, PE12, PE13）三相逆变控制PWM；

K2：RS- 485（PA1, PA2, PA3）USART2；

L2：CAN（PA11, PA12）CAN1；

M2：温湿度传感器（PG3, PG4）I2C口；

软件故障保护后5S后，二次重新起机，可循环启动；

硬件故障10S后，二次重新起机，10min内三次锁死，启动一次（连续运转10S）清除计数；

以下所有故障，软件故障闪红灯，硬件故障常亮红灯；

不允许连带故障出现，只能显示第一故障；

主电继电器吸合后，不允许断开，除非正常断输入电源；

通讯与ptu都能清除锁死故障；

注意：此项目，硬件驱动独立供电，软件无需开启电荷泵功能变量，ChargePumpUsed；

# 1.5 启机逻辑（）

# Step1：上电预充电

\*母线电压≥400V时，2s时延后吸合母线主电继电器（KMON）旁路预充电电阻，完成预充电；

\*后续输入电压  $< 50V$  时（突断输入电源），此时系统处于停机状态，则断母线主继电器，重新等待下一次预充电流程；

# 1.6 散热风扇工作逻辑（）

\*散热风扇为PWM调速，PWM频率：5kHz，高电平有效，PWM调节范围  $0 - 100\%$  占空比，上位机显示占空比数值；

\*上电全速运行10S；

\*PFC模块温度或INV模块温度  $\geq 45^{\circ}C$  时开启散热风扇，初始占空比  $10\% ,100^{\circ}C$  对100占空比；

\*主回路停机后，  $10\%$  占空比运行3min后停机；

# 1.7 母线主继电器吸合逻辑（）

\*母线主继电器由于线包功耗比较大，吸合后为PWM降压保持吸合即可，PWM频率：5kHz，高电平有效；

\*吸合时，  $100\%$  占空比保持1S，1S后  $50\%$  占空比维持；

# 1.8 PFC逐波限流逻辑（）

\*MCU收到PFC_CBC信号时，低电平触发，关闭当前周期内PWM波，直值信号恢复正常；\*由于逐波信号比较短，触发时，PTU能维持点亮5S；

# 1.9 INV逐波限流逻辑()

* MCU收到INV_CBC信号时，低电平触发，关闭当前周期内PWM波，直值信号恢复正常；* 由于逐波信号比较短，触发时，PTU能维持点亮5S；

# 1.10 通讯接口逻辑()

* RS- 485为主通讯，与主控进行数据交互；* CAN为维护接口，接口也会引至维护端子排位置；

# 1.11 LED指示灯逻辑()

A：上电时4个指示灯全亮2S，闪烁2S；

B:RUN灯

a：待机时1Hz闪烁， $50\%$ 占空比；b：PFC与逆变工作时4Hz闪烁， $50\%$ 占空比；c：PTU要映射此灯；

C：485- COM灯（RS- 485通讯指示灯）

a：通讯时1Hz闪烁， $50\%$ 占空比；b：无通讯时则灭；c：PTU要映射此灯；

D：CAN- COM灯（CAN通讯指示灯）

a：通讯时1Hz闪烁， $50\%$ 占空比；

b：无通讯时则灭；

c: PTU要映射此灯；

E: FLT灯

a：软件故障1Hz闪烁，  $50\%$  占空比；b：硬件故障常亮；c：PTU要映射此灯；

# 1.12 输入输出口定义

# 1.12.1DI输入定义（）

\*输入IN1：启动信号；（高电平有效）默认转速750rpm/min（暂定）（无通讯条件下）；

$\ast 0 - 10\mathrm{V}$  ：10V对应1500rpm/min（暂定）  $\geq 1\mathrm{V}$  时开始运转，  $\leq 0.8\mathrm{V}$  时停机；

$\ast 4 - 20\mathrm{mA}$  ：20mA对应1500rpm/min（暂定）  $\geq 7\mathrm{mA}$  时开始运转，  $\leq 5\mathrm{mA}$  时停机；

注：两个模拟量接口软件可配置，默认选择4- 20mA；

\*输入IN2：复位信号（高电平有效），系统复位，该信号只能在IN1为0，系统处于待机时，才能起效，IN2且是待机下由0变1的过程，要有跳变过程，高电平持续时间≥1S;

\*输入IN3：预留；\*输入IN4：预留；

# 1.12.2空压机地址定义（）

add1：模块地址输入1；（高电平有效）add2：模块地址输入2；（高电平有效）

<table><tr><td></td><td>add1</td><td>add2</td></tr><tr><td>1#空压机</td><td>0</td><td>0</td></tr></table>

PVPB09空压机变频器功能规范说明书  

<table><tr><td></td><td>add1</td><td>add2</td></tr><tr><td>2#空压机</td><td>0</td><td>1</td></tr><tr><td>3#空压机</td><td>1</td><td>0</td></tr><tr><td>4#空压机</td><td>1</td><td>1</td></tr></table>

# 1.12.3DO输出定义（）

输出DO1功能定义：运行信号：输出频率≥5Hz吸合；

输出DO2功能定义：故障信号：无电或者故障断开，正常吸合；

输出DO3功能定义：预留；

# 1.13 故障触发逻辑

# 1.13.1交流输入过载保护（）

输入交流过载保护：软件计算输入电压，采集三相输入电流，计算输出容量。

输入容量  $\coloneqq$  三相输入电压\*三相输入电流均值\*1.732f\*功率因素/系统转换效率  $+1000\mathrm{kVA}$  （避免与输出过载冲突）；

变频器额定电流：69.89A（带PFC功率因素为1，系统转换效率取0.95）73.57A，实际以变频器额定电流为保护参考；

当满足以下任一超时条件时：报输出交流过载故障（以下为电机额定电流）。

\*当负载率达到  $110\%$  ，运行600S，电流80.92A;

\*当负载率达到  $120\%$  ，运行450S，电流88.28A;

\*当负载率达到  $130\%$  ，运行300S，电流95.64A;

*当负载率达到  $140\%$  ，运行150S，电流102.99A；*当负载率达到  $150\%$  ，运行90S，电流110.35A；*当负载率达到  $160\%$  ，运行45S，电流117.71A；*当负载率达到  $170\%$  ，运行15S，电流125.06A；*当负载率达到  $180\%$  ，运行5S，电流132.42A；*当负载率达到  $190\%$  ，运行1S，电流139.78A；*当负载率达到  $200\%$  ，运行1S，电流147.14A；

PTU能提示以上对应比例过载过载；

![](images/93da41de2dea77eaf1836fd7fd8f0e3447a38aea6c1bea0290e94dbf7585a777.jpg)

发生以上故障时，停机保护，红灯快闪，同时上位机显示对应故障，10分钟内发生3次同等级故障锁死，可通讯清除锁死故障；

# 1.13.2输出交流过载保护（）

输出交流过载保护：软件计算输出电压，采集三相输出电流，计算输出容量。

输出容量  $\equiv$  三相输出电压\*三相输出电流均值\*1.732f\*功率因素；

电机额定电流：68.37A（变频器通常按0.7的功率因数算输出额定电流）97.67A，实际以电机额定电流为保护参考；

BSM.PVPB09- SW- 000

当满足以下任一超时条件时：报输出交流过载故障（以下为电机额定电流）。

\*当负载率达到  $110\%$  ，运行600S，电流107.44A;\*当负载率达到  $120\%$  ，运行450S，电流117.20A;\*当负载率达到  $130\%$  ，运行300S，电流126.97A;\*当负载率达到  $140\%$  ，运行150S，电流136.74A;\*当负载率达到  $150\%$  ，运行90S，电流146.51A;\*当负载率达到  $160\%$  ，运行45S，电流156.27A;\*当负载率达到  $170\%$  ，运行15S，电流166.04A;\*当负载率达到  $180\%$  ，运行5S，电流175.81A;\*当负载率达到  $190\%$  ，运行1S，电流185.58A;\*当负载率达到  $200\%$  ，运行1S，电流195.34A;

PTU能提示以上对应比例过载过载；

![](images/0d3dcd1cc8bd145cf44e5cc60687dd7cbb113ddc24990a70bd779bbf0605719c.jpg)

发生以上故障时，停机保护，红灯快闪，同时上位机显示对应故障，10分钟内发生3次同等级故障锁死，可通讯清除锁死故障；

# 1.13.3交流过欠压故障（）

交流额定电压三相交流380V，正常工作范围：328V- 453V，系统工作时判断，欠压值软件设置：  $\leq 325\mathrm{V}$  持续2S，滞回为设置值  $+10\mathrm{V}$  ，过压值软件设置：  $\geq 460\mathrm{V} + 3\mathrm{V}$  持续1S，滞回为设置值- 10V;

注：\*支持突切三相输入空开，变频器无故障码上报；\*输入电压  $\leq 30V$  不判断欠压故障；

红灯闪烁，直至故障消除，同时上位机显示对应故障；

# 1.13.4母线过欠压故障（）

1.13.4 母线过欠压故障（）母线直流额定电压 537.3V（380V 三相自然整流电压），母线欠压判断基于工作态时判断，待机下不做判断，欠压值软件设置：  $\leq 250\mathrm{V}$  持续 5s，滞回为设置值  $+5\mathrm{V}$ ，一级过压值软件设置：  $\geq 720\mathrm{V}$  持续 100ms，滞回为设置值  $- 5\mathrm{V}$ ，二级过压值软件设置：  $\geq 750\mathrm{V}$  持续 100ms，滞回为设置值  $- 5\mathrm{V}$ ，三级硬件过压设置：  $\geq 784\mathrm{V}$  立即保护；

发生母线一级、二级故障时，红灯快闪，三级故障红灯长亮，直至故障消除，同时上位机显示对应故障；

# 1.13.5过温故障（）

A：三相共模电感温度采样，  $\geq 105^{\circ}C$  ，持续5S，输出功率降额，只能输出最大频率  $80\%$  ，温

度超过  $105^{\circ}C$  时停机保护，报“三相共模电感过温”，  $\leq 85^{\circ}C$  恢复；

B：PFC电感温度采样，  $\geqslant 105^{\circ}C$  ，持续5S，输出功率降额，只能输出最大频率  $80\%$  ，温度超过  $105^{\circ}C$  时停机保护，报“PFC电感过温”，  $\leq 85^{\circ}C$  恢复；

C：母线电容1或母线电容2温度采样，  $\geqslant 95^{\circ}C$  ，持续5S，输出功率降额，只能输出最大频率  $80\%$  ，温度超过  $100^{\circ}C$  时停机保护，报“母线电容过温”，  $\leq 85^{\circ}C$  恢复；

D：PFC模块温度采样，  $\geqslant 100^{\circ}C$  ，持续5S，输出功率降额，只能输出最大频率  $80\%$  ，温度超过  $105^{\circ}C$  时停机保护，报“PFC模块过温”，  $\leq 85^{\circ}C$  恢复；

E：逆变模块温度采样，  $\geqslant 100^{\circ}C$  ，持续5S，输出功率降额，只能输出最大频率  $80\%$  ，温度超过  $105^{\circ}C$  时停机保护，报“逆变模块过温”，  $\leq 85^{\circ}C$  恢复；

F：辅助电源整流二极管温度采样，温度超过  $\geqslant 105^{\circ}C$  时停机，报“辅助电源整流二极管过温”这个只提示，不参与停机；

发生以上故障时，红灯伪闪，直至故障消除，同时上位机显示对应故障；

# 1.13.6温度传感器故障（）

A：上电时刻，各通道温度值与MCU温度相差  $60^{\circ}C$  ，报相应通道温度传感器故障，不停机，只显示对应通道故障；B：MCU温度与温湿度传感器采样温度均  $\geqslant 100^{\circ}C$  ，报控制室过温，只显示，不参与停机；C：温湿度传感器湿度  $\geqslant 90\%$  ，报控制室湿度过高，只显示，不参与停机；D：两个温度传感器同时故障时，系统停机，两个温度任意  $\leq 85^{\circ}C$  时，系统能正常起机；

发生以上故障时，红灯快闪，同时上位机显示对应故障；

# 1.13.7三相交流输入缺相故障（）

输入缺相故障，以三相输入交流电压输入R相电压采样、输入S相电压采样、输入T相电压采样，其中一相缺相持续xxmS，报“对应相缺相故障”，输入电压小于30V不做判断。

发生以上故障时，红灯快闪，直至故障消除，同时上位机显示对应故障；

# 1.13.85V电源故障（）

该电源是12V通过BUCK降压的5V电压，电压精度  $5\mathrm{V}\pm 0.2\mathrm{V}$  ，所以当检测到5V电源超过5.5V持续≥1S时，报“5V主电源过压故障”5.3V恢复，，当检测到5V电源低于4.5V持续≥1S时，报“5V电源欠压故障”，4.7V恢复；

发生以上故障时，停机保护，红灯快闪，同时上位机显示对应故障，10分钟内发生3次同等级故障则锁死，可通讯清除锁死故障；

# 1.13.912V电源故障（）

该电源是辅助电源闭环电压，当检测到12V电源超过18V持续≥1S时，报“12V主电源过压故障”，停机保护，16V恢复；

当检测到12V电源低于7V持续≥1S时，报“12V电源欠压故障”，停机保护，9V恢复；

发生以上故障时，停机保护，红灯快闪，同时上位机显示对应故障，10分钟内发生3次同等级故障则锁死，可通讯清除锁死故障；

# 1.13.10 24V电源故障（）

该电源是辅助电源闭环电压，当检测到24V电源超过32V持续≥1S时，报“24V主电源过压故障”，停机保护，30V恢复；

当检测到24V电源低于16V持续≥1S时，报“24V电源欠压故障”，停机保护，18V恢复；

发生以上故障时，只提示不参与保护，红灯快闪，同时上位机显示对应故障；

# 1.13.11 输入交流电压采样传感器故障（）

上电时刻R相电压采样，S相电压采样，T相电压采样≥500V或≤200V持续≥1S时，报“对应交流电压采样传感器故障”，基于5V、12V采样正常，只提示，不参与停机；

接3.3V时会报其他故障，只能接0V模拟；

发生以上故障时，红灯快闪，同时上位机显示对应故障；

# 1.13.12 输出交流电压采样传感器故障（）

上电时刻U相电压采样，V相电压采样，W相电压采样≥500V或≤200V持续≥1S时，报“对应交流电压采样传感器故障”，基于5V、12V采样正常，这时不能转速跟踪与启动前缺相与接地保护，只能整停1S后再启动；

接3.3V时会报其他故障，只能接0V模拟；

发生以上故障时，红灯快闪，同时上位机显示对应故障；

# 1.13.13 母线电压传感器故障（）

当上电时刻母线电压采样（VBUS）  $\leq 50V$  或者  $\geq 800V$  ，  $\geq 500\mathrm{ms}$  ，报“母线电压传感器故障”，此时不允许吸合母线继电器；

发生以上故障时，停机保护，红灯换闪，同时上位机显示对应故障，10分钟内发生3次同等级故障则锁死，可通讯清除锁死故障；

# 1.13.14 输入交流电流采样传感器故障（）

当上电时刻输出R相电流采样、输出S相电流采样、输出T相电流采样  $\geq 5A$  ，  $\geq 500\mathrm{ms}$  ，报“输出交流电流传感器故障”，停机保护；

发生以上故障时，停机保护，红灯换闪，同时上位机显示对应故障，10分钟内发生3次同等级故障则锁死，可通讯清除锁死故障；

# 1.13.15 输出交流电流采样传感器故障（）

当上电时刻输出U相电流采样、输出V相电流采样、输出W相电流采样  $\geq 5A$  ，  $\geq 500\mathrm{ms}$  ，报“输出交流电流传感器故障”，停机保护；

发生以上故障时，停机保护，红灯换闪，同时上位机显示对应故障，10分钟内发生3次同等级故障则锁死，可通讯清除锁死故障；

# 1.13.16 输入硬件过流故障（）

MCU检测到I- HD_OC低电平时，报“输入硬件过流故障”；软件能清除故障；

发生以上故障时，对应 MCU 红灯常亮，同时上位机显示对应故障，10S 后重新启动，10 分钟内发生三次锁死，中间运行时间超过 30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.17 输出硬件过流故障（）

MCU 检测到 O- HD_OC 低电平时，报“输出硬件过流故障”；软件能清除故障；

发生以上故障时，对应 MCU 红灯常亮，同时上位机显示对应故障，10S 后重新启动，10 分钟内发生三次锁死，中间运行时间超过 30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.18 母线硬件过压故障（）

MCU 检测到 OVP_P_BUS 低电平时，报“母线硬件过压故障”；软件能清除故障；

发生以上故障时，对应 MCU 红灯常亮，同时上位机显示对应故障，10S 后重新启动，10 分钟内发生三次锁死，中间运行时间超过 30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.19 母线主电继电器故障（）

当接触器正常吸合（KMON），启动 INV 逆变，如果此时母线电压波动超过  $\pm 50\mathrm{V}$ ，且 KMON_FLT 为低电平，报“母线主电继电器”故障；

发运行前缺相时会触发母线硬件过压，无法报出继电器故障；

该故障会触发硬件锁死故障；

发生以上故障时，停机保护，红灯快闪，同时上位机显示对应故障，10 分钟内发生 3 次同等级故障锁死，可通讯清除锁死故障；

# 1.13.20 PFC驱动短路故障（）

MCU检测到F- IPM- PFC_MCU1低电平时，报“PFC驱动短路故障”；软件能清除故障；

发生以上故障时，对应MCU红灯常亮，同时上位机显示对应故障，10S后重新启动，10分钟内发生三次锁死，中间运行时间超过30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.21 逆变输出短路故障（）

MCU检测到F- IPM- INV_MCU1低电平时，报“逆变输出短路故障”；软件能清除故障；

发生以上故障时，对应MCU红灯常亮，同时上位机显示对应故障，10S后重新启动，10分钟内发生三次锁死，中间运行时间超过30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.22 PFC电感温度开关过温故障（）

MCU检测到TPSW由高变低电平持续5S，报“PFC电感温度开关过温故障”；软件能清除故障；

若TPSW一直为低电平，持续时间  $\geqslant 10\mathrm{min}$  ，三相共模电感温度与PFC电感温度均  $\leqslant 100^{\circ}\mathrm{C}$  报“PFC电感温度开关故障”此时强行启机，温度保护以三相共模电感温度与PFC电感温度为准；

发生以上故障时，对应MCU红灯常亮，同时上位机显示对应故障，10S后重新启动，10分钟内发生三次锁死，中间运行时间超过30S，则清除前面计数次数，可通讯清除锁死故障；

# 1.13.23 控制电源掉电故障（）

A：MCU检测到POW由高变低电平时，立即封锁PFC与INV的PWM波，报“控制电源掉电故障”；

B：若POW信号一直失效时，基于5V、12V、24V采样正常，持续10S后，则忽略“控制电

源掉电故障”此时强行启机，只提示该故障；

发生以上故障时，对应MCU红灯闪烁，同时上位机显示对应故障；

# 1.13.24 参考源故障（）

当MCU上电检测到所有的交流端口电压不在  $3.3\mathrm{V} + 1$  内，报“参考源故障”，停机， $3.3\mathrm{V} \pm 0.2$ ，故障恢复；

发生以上故障时，对应MCU红灯快闪，同时上位机显示对应故障；

# 1.13.25 三相交流输出缺相故障（）

系统正常工作，断开任意U、V、W其中一相，持续xxmS，系统应能正常报“对应相的输出缺相”；

注：输出缺相分运行前缺相与运行中缺相，软件默认两种都开启，（PTU可设置）；

发生以上故障时，停机保护，红灯快闪，直至故障消除，同时上位机显示对应故障，10S后重新启动，10分钟内发生三次锁死，可通讯清除锁死故障；

# 1.13.26 输出接地故障（）

输出接地检测方式，分上电接地与运行前检测，运行中无法检测，软件识别到有接地故障时报“对应相的输出接地”；

注：输出接地分上电检测与运行前检测，软件默认上电检测开启，（PTU可设置）；

发生以上故障时，停机保护，红灯快闪，直至故障消除，同时上位机显示对应故障，10分钟第21页共13深圳市波斯曼技术有限公司

内发生三次锁死，可通讯清除锁死故障；

# 1.13.27 主电源接错故障（）

能正常检测到母线电压≥300V，三相输入电压均≤50V，三相输出电压均≥50V，报“主电源接错”不允许启动逆变与吸合吸合母线主继电器；

发生以上故障时，停机保护，红灯快闪，直至故障消除，同时上位机显示对应故障；

# 1.13.28 输入无压（）

有启动信号时，三相各电压≤50V，提示“输入无压”；

发生以上故障时，红灯快闪，直至故障消除，同时上位机显示对应提示；

# 1.13.29 三相交流输入相序错误（）

MCU检测到输入R、S、T相序不正确时，提示“输入相序错误”只提示，不参与停机；

发生以上警示时，红灯不快闪

# 1.13.30 输入频率低&输入频率高（）

MCU检测到输入频率低于47Hz±0.3Hz，≥3S提示“输入频率低”，47.5Hz±0.3Hz持续5s恢复频率。检测到输入频率高于63Hz±0.3Hz，≥3S提示“输入频率高”，47.5Hz±0.3Hz持续5s恢复频率。持续5S恢复，只提示，不参与停机；

# 发生以上警示时，红灯快闪

# 1.13.31 MCU外设故障（）

A：MCU外部存储器故障；B：MCU外部温湿度传感器；

# C：设置参数丢失；

发生以上故障时，MCU红灯快闪，同时上位机显示对应故障；

# 1.13.32 通讯故障（）

RS- 485通讯上后，1min内无数据则认为通讯故障，发生故障时，软件依据默认频率运行/转速；

# 1.14其他信息

# 1.14.1系统载波（）

A：PFC载波：频率默认36kHz，固定载波，死区0.5uS；B：INV载波：频率默认5kHz，设置范围2kHz- 10kHz，死区2uS；C：智能逆变载波调整；

PFC温度或INV温度  $100^{\circ}C$  以内，逆变载波可到设置最大载波频率；

PFC温度或INV温度  $100^{\circ}C - 105^{\circ}C$  设置逆变载波的中间频率，降载波速率，整数400Hz/20s，BSM.PVPB09- SW- 000 第23页共13深圳市波斯曼技术有限公司

非整数  $300\mathrm{Hz} / 20\mathrm{s}$

PFC 温度或 INV 温度  $106^{\circ}\mathrm{C} - 109^{\circ}\mathrm{C}$  逆变可降到最小载波  $2\mathrm{kHz}$ ，降载波速率，整数  $400\mathrm{Hz} / 20\mathrm{s}$ ，非整数  $300\mathrm{Hz} / 20\mathrm{s}$ ；

PFC 温度或 INV 温度低于  $100^{\circ}\mathrm{C}$ ，逆变可再升载波至设置值，升载波速率，整数  $400\mathrm{Hz} / 20\mathrm{s}$ ，非整数  $300\mathrm{Hz} / 20\mathrm{s}$ ；

D：PTU界面可显示当前载波频率；

# 1.15 系统AD采样确认

A：交流采样电压在±1V误差；B：交流电流采样±0.5A误差；C：MCU周边  $\mathrm{AD}\pm 0.1\mathrm{V}$ ；D：外部AI采样±0.5V；

# 1.16 故障记录与日志记录

故障记录要求能记录故障前后4S数据，故障信息内容，要能达到故障定位要求，发生停机故障后，不能带出一连片与本停机无关的内容信息；

日志记录近可能的多记录信息，包括I/O状态信息、通讯信息、内部状态机信息、所以硬件外设状态等；1S记录一次，日志要考虑断电保护日志信息功能；

记录平时功率段，腔内温度等级，通过这个信息统计可以评估出系统可靠性，看产品在平时大部分是否处于轻载还是满载。示意如下图：

![](images/43741a66268bdc7b560c5fa2b9efc5233f6a24c948b96af7a7f5b2f4b275bcd8.jpg)

# 1.17 大数据信息内容

所记录的内容需要记录在独立的存储区内，清除需要密码保护，数据可下载；

<table><tr><td>序号</td><td>记录内容</td><td>单位</td><td>备注</td></tr><tr><td>1</td><td>累计运行时间</td><td>H</td><td>待机时间+工作时间</td></tr><tr><td>2</td><td>AC380V累计工作时间</td><td>H</td><td>工作时间</td></tr><tr><td>3</td><td>DC110V累计工作时间</td><td>H</td><td></td></tr><tr><td>4</td><td>AC380V上电次数</td><td>次</td><td>上电到断电算一次</td></tr><tr><td>5</td><td>DC110V上电次数</td><td>次</td><td>上电到断电算一次</td></tr><tr><td>6</td><td>母线主继电器吸合次数</td><td>次</td><td></td></tr><tr><td>7</td><td>AC380V输入总能耗</td><td>kW·h</td><td></td></tr><tr><td>8</td><td>三相启停次数</td><td>次</td><td>PFC与INV均启动算一次</td></tr><tr><td>9</td><td>散热风扇启停次数</td><td>次</td><td></td></tr><tr><td>10</td><td>散热风扇累计工作时间</td><td>H</td><td></td></tr><tr><td>11</td><td>母线电容1&amp;amp;母线电容240度以下</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>12</td><td>母线电容1&amp;amp;母线电容240-50度</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>13</td><td>母线电容1&amp;amp;母线电容250-60度</td><td>秒</td><td>两温度分开记录</td></tr></table>

<table><tr><td>序号</td><td>记录内容</td><td>单位</td><td>备注</td></tr><tr><td>14</td><td>母线电容1&amp;amp;母线电容2
60-70度</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>15</td><td>母线电容1&amp;amp;母线电容2
70-80度</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>16</td><td>母线电容1&amp;amp;母线电容2
80-90度</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>17</td><td>母线电容1&amp;amp;母线电容2
90-100度</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>18</td><td>母线电容1&amp;amp;母线电容2
100度以上</td><td>秒</td><td>两温度分开记录</td></tr><tr><td>19</td><td>输出容量0-5kVA</td><td>秒</td><td></td></tr><tr><td>20</td><td>输出容量5-10kVA</td><td>秒</td><td></td></tr><tr><td>21</td><td>输出容量10-15kVA</td><td>秒</td><td></td></tr><tr><td>22</td><td>输出容量15-20kVA</td><td>秒</td><td></td></tr><tr><td>23</td><td>输出容量20-22kVA</td><td>秒</td><td></td></tr><tr><td>24</td><td>输出容量22-24kVA</td><td>秒</td><td></td></tr><tr><td>25</td><td>输出容量24-26kVA</td><td>秒</td><td></td></tr><tr><td>26</td><td>输出容量26-28kVA</td><td>秒</td><td></td></tr><tr><td>27</td><td>输出容量28-30kVA</td><td>秒</td><td></td></tr><tr><td>28</td><td>输出容量30-32kVA</td><td>秒</td><td></td></tr><tr><td>29</td><td>输出容量32-34kVA</td><td>秒</td><td></td></tr><tr><td>30</td><td>输出容量34kVA-36kVA</td><td>秒</td><td></td></tr><tr><td>31</td><td>输出容量36kVA-38kVA</td><td>秒</td><td></td></tr><tr><td>32</td><td>输出容量38kVA-40kVA</td><td>秒</td><td></td></tr><tr><td>33</td><td>输出容量40kVA-42kVA</td><td>秒</td><td></td></tr></table>

<table><tr><td>序号</td><td>记录内容</td><td>单位</td><td>备注</td></tr><tr><td>34</td><td>输出容量 42kVA-44kVA</td><td>秒</td><td></td></tr><tr><td>35</td><td>输出容量 44kVA-46kVA</td><td>秒</td><td></td></tr><tr><td>36</td><td>输出容量 46kVA-48kVA</td><td>秒</td><td></td></tr><tr><td>37</td><td>输出容量 48kVA-50kVA</td><td>秒</td><td></td></tr><tr><td>38</td><td>输出容量 50kVA-52kVA</td><td>秒</td><td></td></tr><tr><td>39</td><td>输出容量 52kVA-54kVA</td><td>秒</td><td></td></tr><tr><td>40</td><td>输出容量 54kVA-56kVA</td><td>秒</td><td></td></tr><tr><td>41</td><td>输出容量 56kVA-58kVA</td><td>秒</td><td></td></tr><tr><td>42</td><td>输出容量 58kVA-60kVA</td><td>秒</td><td></td></tr><tr><td>43</td><td>输出容量 60kVA-62kVA</td><td>秒</td><td></td></tr><tr><td>44</td><td>输出容量 62kVA-64kVA</td><td>秒</td><td></td></tr><tr><td>45</td><td>输出容量 64kVA-66kVA</td><td>秒</td><td></td></tr><tr><td>46</td><td>输出容量 66kVA-68kVA</td><td>秒</td><td></td></tr><tr><td>47</td><td>输出容量 68kVA-70kVA</td><td>秒</td><td></td></tr><tr><td>48</td><td>输出容量 70kVA-72kVA</td><td>秒</td><td></td></tr><tr><td>49</td><td>输出容量 72kVA-74kVA</td><td>秒</td><td></td></tr><tr><td>50</td><td>输出容量 74kVA-76kVA</td><td>秒</td><td></td></tr><tr><td>51</td><td>输出容量 76kVA-78kVA</td><td>秒</td><td></td></tr><tr><td>52</td><td>输出容量 78kVA-80kVA</td><td>秒</td><td></td></tr><tr><td>53</td><td>输出容量 &amp;gt;80kVA</td><td>秒</td><td></td></tr></table>

# 1.18 AD校准功能

考虑硬件误差，软件需要对AD通道做校准功能，采用线性系数校准，默认值为1.0。

$$
AD = AD*Coeff_{adca}
$$

校准参数范围， $\pm 0.2$ ，超出校准范围为硬件通道故障，校准参数不能被升级程序擦除；

# 1.19 软件特殊要求功能

记录参数修改事件记录，记录不能被升级程序擦除，需要特殊命令才能被清除；

# 1.20 上位机要求

A：连接上位机时，上位机1S记录一次所有变频器的参数信息，每连一次创建一个文件，文件格式为XLS；

B：能设置本系统的硬件版本信息，单模块硬件配置：

（1）电源板（BSM.PVPB09-PW）：硬件版本、序列号；（2）功率板（BSM.PVPB09-D1）：硬件版本、序列号；（3）电容板（BSM.PVPB09-C1）：硬件版本、序列号；（4）电感板（BSM.PVPB09-L1）：硬件版本、序列号；（5）驱动板（BSM.PVPB09-DR）：，此板有PFC驱动板与INV驱动板，所以要记录两个硬件版本、序列号；

C整机

（1）整机序列号：

(2) 整机硬件版本：

D：软件版本信息，格式Vx.x；E：硬件版本信息，格式Vx.x；F：程序升级的时候系统处于待机状态；

# 1.21 参数拷贝

1.21 参数拷贝根据不同项目，对需要设置的参数信息能拷贝出来，电脑本地存储，通过上位机直接下发到其它新机器上，避免参数设错与漏设；

# 1.22 故障继电器输出条件

1.22 故障继电器输出条件A：故障继电器常开触点，无电、待机、正常时均断开，硬件锁死故障触发；B：非锁死故障，给了启动信号，60S内仍然无法启动，激活故障继电器；