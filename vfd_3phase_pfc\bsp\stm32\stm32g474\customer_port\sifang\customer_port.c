/******************** (C) COPYRIGHT 2023     ***********************************
* File Name          : customer_port.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : 不同客户的接口
********************************************************************************/

#include <rtthread.h>
#include <rtdevice.h>
#include "uapp.h"
#include "customer_port.h"

/**
 * @brief  初始化客户端口相关配置
 * 
 * 
 * @return int 通常返回值未使用，函数执行成功无返回值。
 */
int customer_port_init(void)
{
    vfd.ac_rated_power = VFD_RATED_POWER;
    vfd.dc_rated_power = VFD_RATED_POWER;

    // 设置交流输入功率因数系数为 1.0f
    vfd.coeff_acin_powerfactor  = 1.0f;
    // 设置交流输出功率因数系数为 1.0f
    vfd.coeff_acout_powerfactor = 1.0f;
    
    // 设置正常交流输入过载系数
    vfd.coeff_normal_acin_overload   = 1.175f;
    // 设置正常交流输出过载系数
    vfd.coeff_normal_acout_overload  = 1.175f;
    // 设置紧急直流输入过载系数为 1.2f
    vfd.coeff_emergnc_dcin_overload  = 1.2f;

    // 设置交流输入功率 PID 控制器的输出最大值为 0.0f
    vfd.pid_acin_power.Umax = 0.0f;
    // 设置交流输入功率 PID 控制器的输出最小值为 -0.5f
    vfd.pid_acin_power.Umin = -0.5f;
    // 设置交流输入功率 PID 控制器的比例系数为 0.001f
    vfd.pid_acin_power.Kp = 0.001f;
    // 设置交流输入功率 PID 控制器的积分系数为 0.0005f
    vfd.pid_acin_power.Ki = 0.0005f;
    
    // 设置交流输出功率 PID 控制器的输出最大值为 0.0f
    vfd.pid_acout_power.Umax = 0.0f;
    // 设置交流输出功率 PID 控制器的输出最小值为 -0.5f
    vfd.pid_acout_power.Umin = -0.5f;
    // 设置交流输出功率 PID 控制器的比例系数为 0.001f
    vfd.pid_acout_power.Kp = 0.001f;
    // 设置交流输出功率 PID 控制器的积分系数为 0.0005f
    vfd.pid_acout_power.Ki = 0.0005f;
    
    // 设置直流输入功率 PID 控制器的输出最大值为 0.0f
    vfd.pid_dcin_power.Umax = 0.0f;
    // 设置直流输入功率 PID 控制器的输出最小值为 -0.5f
    vfd.pid_dcin_power.Umin = -0.5f;
    // 设置直流输入功率 PID 控制器的比例系数为 0.001f
    vfd.pid_dcin_power.Kp = 0.001f;
    // 设置直流输入功率 PID 控制器的积分系数为 0.0002f
    vfd.pid_dcin_power.Ki = 0.0002f;
    
    // 设置直流输入电流 PID 控制器的输出最大值为 0.0f
    vfd.pid_dcin_i.Umax = 0.0f;
    // 设置直流输入电流 PID 控制器的输出最小值为 -0.5f
    vfd.pid_dcin_i.Umin = -0.5f;
    // 设置直流输入电流 PID 控制器的比例系数为 0.001f
    vfd.pid_dcin_i.Kp = 0.001f;
    // 设置直流输入电流 PID 控制器的积分系数为 0.00015f
    vfd.pid_dcin_i.Ki = 0.00015f;
    
    // 重新初始化交流输入功率 PID 控制器
    pid_reinit(&vfd.pid_acin_power);
    // 重新初始化交流输出功率 PID 控制器
    pid_reinit(&vfd.pid_acout_power);
    // 重新初始化直流输入功率 PID 控制器
    pid_reinit(&vfd.pid_dcin_power);
    // 重新初始化直流输入电流 PID 控制器
    pid_reinit(&vfd.pid_dcin_i);
    
    // 设置 PFC 电流参考值
    vfd.pfc_IdRef         = AC_IIN_PID_REF*1.414f;
    // 设置 PFC 逆变模式下的电流参考值
    vfd.pfc_invmode_IdRef = 2.00f*1.414f;
    
}

// 导出 customer_port_init 函数，在系统启动时初始化客户端口
INIT_BOARD_EXPORT(customer_port_init);