# VFD变频器故障逻辑阈值修改说明

## 修改概述

根据《PVPB09空压机变频器功能规范说明书》的要求，对app_diag.c中的故障逻辑阈值和注释进行了同步修改，确保代码实现与规范说明书一致。

## 主要修改内容

### 1. 交流输入过欠压故障阈值修改

**customer_port.h中的阈值修改：**

| 参数 | 修改前 | 修改后 | 规范要求 |
|------|--------|--------|----------|
| AC_VIN_OVER | 455V | 463V | ≥463V持续1S |
| AC_VIN_OVER_RESUME | 445V | 453V | 滞回-10V (453V) |
| AC_VIN_LOW | 328V | 325V | ≤325V持续2S |
| AC_VIN_LOW_RESUME | 338V | 335V | 滞回+10V (335V) |

### 2. 母线过欠压故障阈值修改

**customer_port.h中的阈值修改：**

| 参数 | 修改前 | 修改后 | 规范要求 |
|------|--------|--------|----------|
| VBUS_OVER1 | 750V | 720V | ≥720V持续100ms |
| VBUS_OVER2 | 800V | 750V | ≥750V持续100ms |
| VBUS_RESUME | 715V | 715V | 滞回-5V (715V) - 保持不变 |
| VBUS_LOW | 460V | 250V | ≤250V持续5s |
| VBUS_LOW_RESUME | 470V | 255V | 滞回+5V (255V) |

### 3. 过流保护参数修改

**customer_port.h中的过流参数修改：**

| 参数 | 修改前 | 修改后 | 规范要求 |
|------|--------|--------|----------|
| AC_IIN_OVER1 | 5.83A | 117.2A | 变频器额定电流97.67A，120%过载保护 |
| AC_IIN_OVER2 | 7.0A | 195.34A | 变频器额定电流97.67A，200%严重过载保护 |
| AC_IIN_RESUME | 4.86A | 97.67A | 变频器额定电流97.67A |
| AC_IIN_PID_REF | 5.0A | 97.67A | 变频器额定电流97.67A |
| AC_IOUT_RESUME | 4.86A | 97.67A | 电机额定电流97.67A |
| AC_IOUT_PID_REF | 6.0A | 97.67A | 电机额定电流97.67A |

**注意：统一采用97.67A作为变频器和电机的唯一额定电流值**

### 4. app_diag.c中的注释修改

#### 4.1 故障检测函数注释更新

**交流输入过压故障检测：**
```c
/**
  * @brief 交流输入过压故障检测 - 规范要求：≥463V持续1S，滞回453V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_Over(uint8_t *diag,int offset)
```

**交流输入欠压故障检测：**
```c
/**
  * @brief 交流输入欠压故障检测 - 规范要求：≤325V持续2S，滞回335V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_Low(uint8_t *diag,int offset)
```

**母线过压故障检测：**
```c
/**
  * @brief 母线一级过压故障检测 - 规范要求：≥720V持续100ms，滞回715V恢复
  */
void DiagLgc_Vbus_Over1(uint8_t *diag,int offset)

/**
  * @brief 母线二级过压故障检测 - 规范要求：≥750V持续100ms，滞回745V恢复
  */
void DiagLgc_Vbus_Over2(uint8_t *diag,int offset)
```

**母线欠压故障检测：**
```c
/**
  * @brief 母线欠压故障检测 - 规范要求：≤250V持续5s，滞回255V恢复
  */
void DiagLgc_Vbus_Low(uint8_t *diag,int offset)
```

#### 4.2 过载保护注释更新

**输入交流过载保护：**
```c
/**
  * @brief 输入交流过载保护1 - 规范要求：变频器额定电流97.67A，过载保护
  */
void DiagLgc_ACIin_Over1(uint8_t *diag,int offset)

/**
  * @brief 输入交流过载保护2 - 规范要求：变频器额定电流97.67A，严重过载保护
  */
void DiagLgc_ACIin_Over2(uint8_t *diag,int offset)
```

**输出交流过载保护：**
```c
/**
  * @brief 输出交流过载保护1 - 规范要求：电机额定电流97.67A，过载保护
  */
void DiagLgc_ACIout_Overload1(uint8_t *diag,int offset)

/**
  * @brief 输出交流过载保护2 - 规范要求：电机额定电流97.67A，严重过载保护
  */
void DiagLgc_ACIout_Over2(uint8_t *diag,int offset)
```

#### 4.3 频率故障检测注释更新

```c
/**
  * @brief 输入频率过低故障检测 - 规范要求：<47Hz持续3S，47.5Hz持续5s恢复
  */
void DiagLgc_ACVin_FreqLow(uint8_t *diag,int offset)

/**
  * @brief 输入频率过高故障检测 - 规范要求：>63Hz持续3S，62.5Hz持续5s恢复
  */
void DiagLgc_ACVin_FreqOver(uint8_t *diag,int offset)
```

#### 4.4 电源故障检测注释更新

```c
/**
  * @brief 15V电源过压故障检测 - 规范要求：>18V持续1S，滞回17V恢复
  */
void DiagLgc_15V_Over(uint8_t *diag,int offset)

/**
  * @brief 15V电源欠压故障检测 - 规范要求：<12V持续1S，滞回13V恢复
  */
void DiagLgc_15V_Low(uint8_t *diag,int offset)

/**
  * @brief 5V电源过压故障检测 - 规范要求：>5.5V持续1S，滞回5.3V恢复
  */
void DiagLgc_5V_Over(uint8_t *diag,int offset)

/**
  * @brief 5V电源欠压故障检测 - 规范要求：<4.5V持续1S，滞回4.7V恢复
  */
void DiagLgc_5V_Low(uint8_t *diag,int offset)
```

### 5. 故障表注释更新

在diag_table[]数组中添加了详细的注释说明：

```c
{7,     LEV_2,     0, SOFT,               100,                500,    "DiagLgc_ACVin_Over"},      // ≥463V持续1S，滞回453V
{8,     LEV_2,     0, SOFT,               200,                500,    "DiagLgc_ACVin_Low"},       // ≤325V持续2S，滞回335V
{9,     LEV_2,     0, SOFT,                10,                500,    "DiagLgc_Vbus_Over1"},      // ≥720V持续100ms，滞回715V
{10,    LEV_2,     0, SOFT,                10,                500,    "DiagLgc_Vbus_Over2"},      // ≥750V持续100ms，滞回745V
{11,    LEV_2,     0, SOFT,               500,                500,    "DiagLgc_Vbus_Low"},        // ≤250V持续5s，滞回255V
{28,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_FreqLow"},   // <47Hz持续3S，47.5Hz恢复
{29,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_FreqOver"},  // >63Hz持续3S，62.5Hz恢复
{32,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_15V_Over"},        // >18V持续1S，滞回17V
{33,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_15V_Low"},         // <12V持续1S，滞回13V
{34,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_5V_Over"},         // >5.5V持续1S，滞回5.3V
{35,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_5V_Low"},          // <4.5V持续1S，滞回4.7V
```

## 修改原则

1. **保持原有逻辑不变**：只修改阈值参数和注释，不改变故障检测的基本逻辑
2. **与规范说明书一致**：所有阈值和时间参数都严格按照规范说明书要求设置
3. **注释详细准确**：每个故障检测函数都添加了详细的注释说明，包括具体的阈值和时间要求
4. **滞回特性明确**：明确标注了每个故障的滞回值，防止故障抖动

## 验证建议

1. **功能测试**：建议对修改后的故障阈值进行实际测试验证
2. **边界测试**：特别测试阈值边界附近的故障触发和恢复行为
3. **滞回测试**：验证滞回特性是否正常工作，防止故障抖动
4. **时间测试**：验证故障持续时间是否符合规范要求

## 注意事项

1. **母线欠压阈值变化较大**：从460V降低到250V，需要特别注意测试
2. **交流输入电压阈值调整**：可能影响系统在电网波动时的稳定性
3. **过流参数大幅调整**：
   - 输入过流从5.83A/7.0A调整到117.2A/195.34A，变化巨大
   - 输出过流从4.86A/6.0A调整到97.67A
   - **统一采用97.67A作为变频器和电机的唯一额定电流值**
4. **所有时间参数保持不变**：因为当前设置已经符合规范要求
5. **建议充分测试**：特别是过流保护的实际触发点和保护效果
