/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_app.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : this is vfd system main logic
                        * achieve at least init/logic/control/start/stop ...

********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>

#define UAPP_MACRO
#include "uapp.h"

#include <board.h>

#define DBG_SECTION_NAME               "uapp"
#define DBG_LEVEL                      DBG_LOG
#include <rtdbg.h>

/* Private variables ------------------------------------------------------------*/

/* Private variables end---------------------------------------------------------*/

/* Private function prototypes --------------------------------------------------*/
static void vfd_in_pin_read(void);
static void vfd_out_pin_write(void);
void vfd_load_reducing(void);
extern int modbus_data_update(void);
int vfd_inv_mode(void);
/* Private function prototypes end-----------------------------------------------*/
int vfd_logdata_update(uint8_t *buff);
int vfd_logrtc_update(uint8_t *buff);
void vfd_record_st(void);
void vfd_condition_delay(void);
void vbus_kmon1_set(uint8_t chnn, uint8_t active);
static void vfd_clear_diagstopcnter(void);
void vfd_kmon_logic(void);

const char *state_string[] =
{
    "Idle",
    "Stop",
    "Start",
    "Precharge",
    "Softup",
    "Run",
    "Error",
    "ErrorLock"
};

/**
  * @brief vfd_init
  * @param
  * @retval
  */
int vfd_init(void)
{
    vfd.soft_ver_main    = VFD_SOFT_MAIN;
    vfd.soft_ver_sub     = VFD_SOFT_SUB;
    vfd.paramt_fixup_magic   = PARAM_FIXUP_NUM;
    vfd.soft_ver_test    = 0;

    vfd.log_size = sizeof(record_log_t);

    record_logdata_update_hookset(vfd_logdata_update);
    record_logrtc_update_hookset(vfd_logrtc_update);

    // 初始化4-20mA控制配置参数默认值
    vfd.aux1_config.start_enable = 0;          // 默认禁用4-20mA启停控制
    vfd.aux1_config.speed_enable = 0;          // 默认禁用4-20mA转速控制
    vfd.aux1_config.speed_min = 0;             // 4mA对应最小转速0RPM
    vfd.aux1_config.speed_max = 1500;          // 20mA对应最大转速1500RPM
    vfd.aux1_config.start_threshold = 700;     // 启动阈值7.0mA
    vfd.aux1_config.stop_threshold = 500;      // 停止阈值5.0mA

    vfd.inverter->ops->init();
    vfd.pfc->ops->init(); //last init ,TIM8 default 10K
    vfd.io.F_HD = 1;
    vfd.io.F_IPM_INV = 1;
    vfd.io.F_IPM_PFC = 1;
    vfd.io.F_IPM_DC  = 1;
}

int vfd_machine_task(void)
{
    static uint8_t prev_st = 0;
    uint8_t vbus_ready, vbus_not_ready, condition;
    static uint32_t vbus_ready_cnt;
    static uint32_t vbus_err_cnt;
    uint8_t manual_set_flag = 0;

    modbus_data_update();

    vfd_cooling_fan_logic();
    vfd_clear_diagstopcnter();

    vfd_record_st();

    if (prev_st != vfd.ctrl.sys_st)
    {
        prev_st = vfd.ctrl.sys_st;
        vbus_ready_cnt = 0;
        vbus_err_cnt = 0;
    }


    // state exchange
    if (vfd.ctrl.sys_st == ST_ERROR_LOCK)
    {
        vfd.ctrl.sys_st = ST_ERROR_LOCK;
    }
    else if (vfd_get_fault_stop())
    {
        vfd.ctrl.sys_st = ST_ERROR;
    }
    else if (vfd.bit.soft_stop || vfd.is_update_fw)
    {
        vfd.ctrl.sys_st = ST_STOP;
    }
    else if (vfd.ctrl.start == 0)
    {
        vfd.ctrl.sys_st = ST_STOP;
    }

    vfd_load_reducing();

    vfd_kmon_logic();

    vfd_inv_mode();
    vfd_condition_delay();

#if (DEBUG_USE_IN123_CMD)
    manual_set_flag = 1;
#else
    // state control
    manual_set_flag = vfd.manual.start_pfc_mask || vfd.manual.start_inv_mask || vfd.manual.start_dcdc_mask;
#endif

    if (manual_set_flag == 0)
    {
        switch (vfd.ctrl.sys_st)
        {
        case ST_STOP:

            vfd.ctrl.start_inv = 0;

            if (vfd.ctrl.start)
            {
                vfd.ctrl.sys_st = ST_START;
            }

            break;

        case ST_START:

            if (vfd.bit.kmon1_actived)
            {
                if (vfd.bit.stop_ready)
                    vfd.ctrl.sys_st = ST_RUN;
            }
            else
            {
                vfd.ctrl.start_inv = 0;
            }

            break;

        case ST_RUN:

            if (vfd.bit.kmon1_actived
                    && (mcsdk.CtrlMode != CTRL_MODE_STOP)
               )
            {
                if (vfd.bit.stop_ready || vfd.manual.start_mask || vfd.manual.in1_mask)
                    vfd.ctrl.start_inv = 1;
            }
            else
            {
                vfd.ctrl.start_inv = 0;
            }

            break;

        case ST_ERROR:

            vfd.ctrl.start_inv = 0;

            if (!vfd_get_fault_stop())
                vfd.ctrl.sys_st = ST_STOP;
            break;

        case ST_ERROR_LOCK:

            vfd.ctrl.start_inv = 0;

            if (!vfd.bit.lock_stop)
                vfd.ctrl.sys_st = ST_STOP;

            break;

        default:
            vfd.ctrl.start_inv = 0;

            vfd.ctrl.sys_st = ST_STOP;
            break;
        }
    }

#ifndef FOUT_QUADRANT
    if (vfd.ctrl.start_inv && vfd.bit.system_init)
    {
        vfd.inverter->ops->start();
    }
    else //if(vfd.bit.in_stop_ready)
#endif
    {
        vfd.inverter->ops->stop();
    }

#ifndef VFD_TEST_DEBUG

    vfd.ctrl.start_pfc_invmode = 0;

    if (vfd.ctrl.start_pfc && vfd.bit.system_init && vfd.bit.kmon1_actived)
#else
    if ((vfd.ctrl.start_pfc || vfd.ctrl.start_pfc_invmode)
            && vfd.bit.system_init)
#endif
    {
        vfd.pfc->ops->start();
    }
    else
    {
        vfd.pfc->ops->stop();
    }


    vfd.ctrl.start_pfc_invmode = (vfd.ctrl.start_pfc) ? 0 : vfd.ctrl.start_pfc_invmode;

    vfd.ctrl.inv_st = vfd.inverter->ControlState;
    vfd.ctrl.pfc_st = vfd.pfc->ControlState;
    return 0;
}


void vfd_condition_delay(void)
{
    uint8_t active = 0;
    static uint32_t vbus_ready_cnt = 0;
    static uint32_t kmon1_actived_cnt = 0;
    static uint32_t vac_ready_cnt = 0;
    static uint32_t stop_ready_cnt = 0;
    static uint32_t in_stop_ready_cnt = 0;

    active = !vfd.ctrl.start_pfc        &&
             !vfd.ctrl.start_inv;
    vfd.bit.stop_ready = single_filter(vfd.bit.stop_ready, active, &stop_ready_cnt, 100);

    active = (!vfd.ctrl.start_pfc    &&
              !vfd.ctrl.start_dcdc) || (!vfd.ctrl.start_inv) ;
    vfd.bit.in_stop_ready = single_filter(vfd.bit.in_stop_ready, active, &in_stop_ready_cnt, 20);

    active = (vfd.filter_ad.ac_vin_r >= AC_VIN_LOW) &&
             (vfd.filter_ad.ac_vin_s >= AC_VIN_LOW) &&
             (vfd.filter_ad.ac_vin_t >= AC_VIN_LOW) &&
             (vfd.filter_ad.vbus_inv >= (1.27f * vfd.filter_ad.ac_vin_r));
    vfd.bit.vac_ready = single_filter(vfd.bit.vac_ready, active, &vac_ready_cnt, 250);

    if (vfd.ctrl.start_pfc)
    {
        uint16_t vbus_ref = DPLL3P.SysPar.UserVdcref * 985 / 100;

        active = ((vfd.filter_ad.vbus_inv >= (vbus_ref)) && (vfd.pfc->ControlState == ST_RUN));
    }
    else
        active = (vfd.filter_ad.vbus_inv >= 400.0f);


    vfd.bit.vbus_ready = single_filter(vfd.bit.vbus_ready, active, &vbus_ready_cnt, 100);

    vfd.bit.freq_ready = vfd.bit.isr_freq_ready;

    vfd.bit.kmon1_actived = single_filter(vfd.bit.kmon1_actived, vfd.ctrl.kmon1, &kmon1_actived_cnt, 100);

    vfd.bit.precharge_ready = vfd.bit.kmon1_actived;
}

void vfd_kmon_logic(void)
{
    static uint16_t kmon3_delay = 0;
    static uint8_t freq_off_kmon1 = 0;
    static uint16_t freq_off_kmon1_cnt = 0;

    if (vfd.abs_fast_acin_freq < 40.0f)
    {
        if (freq_off_kmon1_cnt < 10)
            freq_off_kmon1_cnt++;
        else
            freq_off_kmon1 = 1;
    }
    else
    {
        if (freq_off_kmon1_cnt > 0)
            freq_off_kmon1_cnt--;
        else
            freq_off_kmon1 = 0;
    }

    // normal mode
    {
        if (freq_off_kmon1)
        {
            if (!INVERTER_IS_RUN && !PFC_IS_RUN)
                vbus_kmon1_set(KMONx_CHNN1, 0);
        }
        else if (VFD_ENABLE_AC380              &&
                 !vfd.diag.dc_24               &&
                 !vfd.diag.dc_87               &&
                 !vfd.diag.dc_77               &&
                 (vfd.filter_ad.ac_vin_r > AC_VIN_VALID) &&
                 (vfd.filter_ad.ac_vin_s > AC_VIN_VALID) &&
                 (vfd.filter_ad.ac_vin_t > AC_VIN_VALID) &&
                 !vfd.bit.kmon1_actived)
        {

            if (vfd.filter_ad.vbus_inv > (1.2f * vfd.filter_ad.ac_vin_r))
            {
                vbus_kmon1_set(KMONx_CHNN1, 1);
            }

        }
        else
        {
            if ((!INVERTER_IS_RUN && !PFC_IS_RUN) && vfd.bit.in_stop_ready &&
                    ((vfd.fast_ad.ac_vin_r  < AC_VIN_VALID)    ||
                     (vfd.fast_ad.ac_vin_s < AC_VIN_VALID)      ||
                     (vfd.fast_ad.ac_vin_t < AC_VIN_VALID)      ||
                     (vfd.filter_ad.vbus_inv < (1.2f * vfd.filter_ad.ac_vin_r)))
               )
                vbus_kmon1_set(KMONx_CHNN1, 0);

        }
    }

    if (vfd.diag.dc_78 && !INVERTER_IS_RUN && !PFC_IS_RUN)
    {
        vfd.manual.kmon1_mask = 0;
        vfd.ctrl.kmon1 = 0;
    }

#ifdef VFD_TEST_DEBUG
    if (vfd.manual.start_pfc_mask)
    {
        if (((vfd.ctrl.start_pfc) || (vfd.ctrl.start_pfc_invmode)) && !vfd.ctrl.start)
            vbus_kmon1_set(KMONx_CHNN3, 1);
        else
            vbus_kmon1_set(KMONx_CHNN3, 0);
    }

#endif
}


void vfd_record_st(void)
{
    static uint8_t vfd_start_prev = 0;
    static uint8_t prev_inv_mode = 0;
    static uint8_t prev_pfc_st = 0;
    static uint8_t prev_llc_st = 0;

    if (vfd_start_prev != vfd.ctrl.start)
    {
        if (vfd.ctrl.start)
            record_logdata_push(LOG_SetPvpbStart, 0);
        else
            record_logdata_push(LOG_SetPvpbStop, 0);

        vfd_start_prev = vfd.ctrl.start;
    }

    if (prev_pfc_st != vfd.ctrl.pfc_st)
    {
        prev_pfc_st = vfd.ctrl.pfc_st;
        record_logdata_push(LOG_PfcSt, prev_pfc_st);
    }

    if (prev_inv_mode != vfd.ctrl.sys_mode)
    {
        prev_inv_mode = vfd.ctrl.sys_mode;
        record_logdata_push(LOG_SysSt, prev_inv_mode);
    }

    if (prev_llc_st != vfd.ctrl.llc_st)
    {
        prev_llc_st = vfd.ctrl.llc_st;
        record_logdata_push(LOG_BoostSt, prev_llc_st);
    }
}

uint8_t overtemp_flag = 0;
uint8_t coolfanerr_flag = 0;
/**
  * @brief vfd_load_reducing
  * @param
  * @retval
  */
void vfd_load_reducing(void)
{
    static uint32_t cnt = 0;

    uint8_t load_reducing = 0;
    static uint32_t flag_filtercnt = 0;
    float err = 0, err1 = 0, err2 = 0;

    cnt++;

    if (cnt % 10 != 0)    return; //100ms


    if (vfd.diag.dc_52)      overtemp_flag = 1;
    else if (vfd.diag.dc_53) overtemp_flag = 2;
    else if (vfd.diag.dc_54) overtemp_flag = 3;
    else if (vfd.diag.dc_55) overtemp_flag = 4;
    else if (vfd.diag.dc_56) overtemp_flag = 5;
    else if (vfd.diag.dc_57) overtemp_flag = 6;
    else if (vfd.diag.dc_58) overtemp_flag = 7;
    else if (vfd.diag.dc_59) overtemp_flag = 8;
    else if (vfd.diag.dc_60) overtemp_flag = 9;
    else if (vfd.diag.dc_61) overtemp_flag = 10;
    else    overtemp_flag = 0;


    if ((overtemp_flag == 0) && (coolfanerr_flag == 0))
    {
        err = 0;
        err1 = 0;
        err2 = 0;
        err = (vfd.ac_rated_power * vfd.coeff_normal_acout_overload - vfd.acout_capacity) * 0.001f;
        if ((vfd.pid_acout_power.Out != 0) && (fabs(err) <= 0.1f))
            err = 0;

        err1 = (vfd.ac_rated_power * vfd.coeff_normal_acin_overload - vfd.acin_capacity) * 0.001f;
        if ((vfd.pid_acin_power.Out != 0) && (fabs(err1) <= 0.1f))
            err1 = 0;

        err2 = (vfd.dc_rated_power * vfd.coeff_emergnc_dcin_overload - vfd.dcdc_power) * 0.001f;

        if ((vfd.pid_dcin_power.Out != 0) && (fabs(err2) <= 0.05f))
            err2 = 0;
    }
    else
    {
        err = (vfd.ac_rated_power * 0.5f - vfd.acout_capacity) * 0.001f;
        if ((vfd.pid_acout_power.Out != 0) && (fabs(err) <= 0.1f))
            err = 0;

        err1 = (vfd.ac_rated_power * 0.5f - vfd.acin_capacity) * 0.001f;
        if ((vfd.pid_acin_power.Out != 0) && (fabs(err1) <= 0.1f))
            err1 = 0;

        err2 = (vfd.dc_rated_power * 0.5f - vfd.dcdc_power) * 0.001f;

        if ((vfd.pid_dcin_power.Out != 0) && (fabs(err2) <= 0.05f))
            err2 = 0;
    }

    if (vfd.ctrl.start_inv)  pid_control(&vfd.pid_acout_power, err);
    else    pid_reinit(&vfd.pid_acout_power);

    if (vfd.ctrl.start_pfc)  pid_control(&vfd.pid_acin_power, err1);
    else    pid_reinit(&vfd.pid_acin_power);


    if ((vfd.pid_acin_power.Out != 0) || (vfd.pid_acout_power.Out != 0))
    {
        load_reducing = 1;
    }
    else
    {
        load_reducing = 0;
    }

    if (load_reducing)
    {
        vfd.ctrl.load_reducing = 1;
        flag_filtercnt = 0;
    }
    else
    {
        if (flag_filtercnt < 10)
            flag_filtercnt++;
        else
            vfd.ctrl.load_reducing = 0;
    }
}


int vfd_inv_mode(void)
{
    // ????
    if ((vfd.ctrl.sys_st == ST_ERROR)
            && (vfd.bit.lock_stop))
    {
        vfd.ctrl.sys_mode = INV_MODE_ERR_LOCK ;  //????????
    }
    else if (vfd.ctrl.sys_st == ST_ERROR)
    {
        vfd.ctrl.sys_mode = INV_MODE_ERR_STOP ;  //???????
    }
    else if (vfd.ctrl.sys_st == ST_STOP)
        vfd.ctrl.sys_mode = INV_MODE_STOP ;  //???
    else if ((vfd.ctrl.sys_st == ST_RUN)
             && vfd.ctrl.load_reducing
             && (overtemp_flag != 0))
    {
        if (overtemp_flag == 1)
            vfd.ctrl.sys_mode = INV_MODE_OVER_TEMP_1 ;
        else if (overtemp_flag == 2)
            vfd.ctrl.sys_mode = INV_MODE_OVER_TEMP_2 ;
        else if (overtemp_flag == 3)
            vfd.ctrl.sys_mode = INV_MODE_OVER_TEMP_3 ;
        else if (overtemp_flag == 4)
            vfd.ctrl.sys_mode = INV_MODE_OVER_TEMP_4 ;
        else
            vfd.ctrl.sys_mode = INV_MODE_TEMP_LIMIT ;
    }
    else if (0
            )
    {
        vfd.ctrl.sys_mode = INV_MODE_OUTPUT_LIMIT ;  //???????
    }
    else if (vfd.ctrl.start && (vfd.ctrl.sys_st == ST_RUN))
        vfd.ctrl.sys_mode = INV_MODE_RUN ;  //????
    else
        vfd.ctrl.sys_mode = INV_MODE_STOP ;
}

/**
  * @brief vfd_io_control
  * @param
  * @retval
  */
int vfd_io_control(void)
{
    static uint32_t start_timeout = 0;

    uint8_t timeout_flag = !INVERTER_IS_RUN && vfd.io.in1;
    vfd.bit.start_timeout = single_filter(vfd.bit.start_timeout, timeout_flag, &start_timeout, 6000);

    if (!vfd_get_fault_stop())
        vfd.ctrl.o1 = 1;
    else
        vfd.ctrl.o1 = 0;

    vfd.ctrl.o2 = 0;

    vfd_in_pin_read();  // - ?????????????
    vfd_out_pin_write();

    vfd.bit.com_can = com_can.flag_normal;
    vfd.bit.com_485 = com_485.flag_normal;

    vfd.bit.freq_revert = ((vfd.pfc->Freq >= -65) && (vfd.pfc->Freq <= -45));
}

/**
  * @brief vfd_operation_statistics
  * @param
  * @retval
  */
int vfd_operation_statistics(void)
{   
    static uint8_t last_acvin_ready = 0;
    static uint8_t last_dcin_ready = 0;
    static uint8_t last_buskmon = 0;
    //static uint8_t last_fenState = 0;
    static uint8_t last_run_flag = 0;
    static uint8_t first = 1;
    static uint32_t sec_tick = 0;
    float power = 0;

    float iac_avg = 0;
    float acIout = 0;
    float acIin = 0;
    float acVin = 0;

    acIout = (vfd.filter_ad.ac_iout_u + vfd.filter_ad.ac_iout_v + vfd.filter_ad.ac_iout_w) / 3;
    acIin = (vfd.filter_ad.ac_iin_r + vfd.filter_ad.ac_iin_s + vfd.filter_ad.ac_iin_t) / 3;
    acVin = (vfd.filter_ad.ac_vin_r + vfd.filter_ad.ac_vin_s + vfd.filter_ad.ac_vin_t) / 3;

    float acin_capacity    = acVin * acIin * 1.732f;
    float acout_capacity   = vfd.filter_ad.ac_vout * acIout * 1.732f;

    vfd.dcdc_power  = 0;

    if (PFC_IS_RUN && (AC380_FreqIs_Ok))
        vfd.acin_capacity  = F32_FoLPFilter(0.05f, 500, vfd.acin_capacity, acin_capacity);
    else
        vfd.acin_capacity  = 0;

    if (INVERTER_IS_RUN)
        vfd.acout_capacity = F32_FoLPFilter(0.05f, 500, vfd.acout_capacity, acout_capacity);
    else
        vfd.acout_capacity = 0;

    vfd.acin_power  = vfd.acin_capacity  * vfd.coeff_acin_powerfactor;
    vfd.acout_power = vfd.acout_capacity * vfd.coeff_acout_powerfactor;

    if (rt_tick_get() / RT_TICK_PER_SECOND != sec_tick)
    {
        sec_tick = rt_tick_get() / RT_TICK_PER_SECOND;

        if (first)
        {
            first = 0;
            nvs_datas.accumulator.dataU32[0] += rt_tick_get() / RT_TICK_PER_SECOND;// total poweron tick
        }
        else
            nvs_datas.accumulator.dataU32[0]++;// total poweron tick

        if (VFD_INPUT_AC != last_acvin_ready)
        {
            last_acvin_ready = VFD_INPUT_AC;
            if (VFD_INPUT_AC)
                nvs_datas.accumulator.dataU32[3]++; // acvin on cnter
        }

        if (VFD_INPUT_DC != last_dcin_ready)
        {
            last_dcin_ready = VFD_INPUT_DC;
            if (VFD_INPUT_DC)
                nvs_datas.accumulator.dataU32[4]++; // dcvin on cnter
        }


        if (vfd.ctrl.kmon1 != last_buskmon)
        {
            last_buskmon = vfd.ctrl.kmon1;
            if (vfd.ctrl.kmon1)
                nvs_datas.accumulator.dataU32[5]++; // vbus kmon on cnter
        }
        
        //鏀鹃�庢墖鍚�鍋滄�℃暟鍒ゆ柇
        // if (vfd.ctrl.fen != last_fenState)
        // {
        //     last_fenState = vfd.ctrl.kmon1;
        //     if (vfd.ctrl.fen)
        //         nvs_datas.accumulator.dataU32[7]++; // fen start-stop cnter
        // }
        //椋庢墖绱�璁″伐浣滄椂闂�
            //nvs_datas.accumulator.dataU32[8]++;       //Cumulative operating time of the fan
        
        //
        if (last_run_flag != INVERTER_IS_RUN)
        {
            last_run_flag = INVERTER_IS_RUN;
            if (INVERTER_IS_RUN)
                nvs_datas.accumulator.dataU32[6]++; // inverter run on cnter
        }

        if (INVERTER_IS_RUN)
        {
            rt_int16_t temp = 0;

            float power = vfd.acout_capacity;//W

            power = power / 3600; //W*h

            vfd.motor_run_sec++;

            vfd.powerconsumption_now    += power;
            if (VFD_INPUT_AC)
            {
                nvs_datas.accumulator.dataF32[0] += power;
                nvs_datas.accumulator.dataU32[1]++; // AC run tick
            }
            else if (VFD_INPUT_DC)
            {
                nvs_datas.accumulator.dataF32[1] += power;
                nvs_datas.accumulator.dataU32[2]++; // DC run tick
            }

            // -- Bus Cap temp
            if ((vfd.filter_ad.temp_BUS_CAP) < 400)            nvs_datas.accumulator.dataU32[10]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 500)       nvs_datas.accumulator.dataU32[11]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 600)       nvs_datas.accumulator.dataU32[12]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 700)       nvs_datas.accumulator.dataU32[13]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 800)       nvs_datas.accumulator.dataU32[14]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 900)       nvs_datas.accumulator.dataU32[15]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) < 1000)      nvs_datas.accumulator.dataU32[16]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP) >= 1000)     nvs_datas.accumulator.dataU32[17]++; //
            // -- Bus Cap2 temp
            if ((vfd.filter_ad.temp_BUS_CAP2) < 400)            nvs_datas.accumulator.dataU32[18]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 500)       nvs_datas.accumulator.dataU32[19]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 600)       nvs_datas.accumulator.dataU32[20]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 700)       nvs_datas.accumulator.dataU32[21]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 800)       nvs_datas.accumulator.dataU32[22]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 900)       nvs_datas.accumulator.dataU32[23]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) < 1000)      nvs_datas.accumulator.dataU32[24]++; //
            else if ((vfd.filter_ad.temp_BUS_CAP2) >= 1000)     nvs_datas.accumulator.dataU32[25]++; //  
                      
            // --capacity  -- 杈撳嚭瀹归噺-- /
            if (vfd.acout_capacity < 5000.0f)         nvs_datas.accumulator.dataU32[26]++; //
            else if (vfd.acout_capacity < 1500.0f)    nvs_datas.accumulator.dataU32[27]++; //
            else if (vfd.acout_capacity < 10000.0f)    nvs_datas.accumulator.dataU32[28]++; //
            else if (vfd.acout_capacity < 15000.0f)    nvs_datas.accumulator.dataU32[29]++; //
            else if (vfd.acout_capacity < 20000.0f)    nvs_datas.accumulator.dataU32[30]++; //
            else if (vfd.acout_capacity < 22000.0f)    nvs_datas.accumulator.dataU32[31]++; //
            else if (vfd.acout_capacity < 24000.0f)    nvs_datas.accumulator.dataU32[32]++; //
            else if (vfd.acout_capacity < 26000.0f)    nvs_datas.accumulator.dataU32[33]++; //
            else if (vfd.acout_capacity < 28000.0f)    nvs_datas.accumulator.dataU32[34]++; //
            else if (vfd.acout_capacity < 30000.0f)    nvs_datas.accumulator.dataU32[35]++; //
            else if (vfd.acout_capacity < 32000.0f)    nvs_datas.accumulator.dataU32[36]++; //
            else if (vfd.acout_capacity < 34000.0f)    nvs_datas.accumulator.dataU32[37]++; //
            else if (vfd.acout_capacity < 36000.0f)    nvs_datas.accumulator.dataU32[38]++; //
            else if (vfd.acout_capacity < 38000.0f)    nvs_datas.accumulator.dataU32[39]++; //
            else if (vfd.acout_capacity < 40000.0f)    nvs_datas.accumulator.dataU32[40]++; //
            else if (vfd.acout_capacity < 42000.0f)    nvs_datas.accumulator.dataU32[41]++; //
            else if (vfd.acout_capacity < 44000.0f)    nvs_datas.accumulator.dataU32[42]++; //
            else if (vfd.acout_capacity < 46000.0f)    nvs_datas.accumulator.dataU32[43]++; //
            else if (vfd.acout_capacity < 48000.0f)    nvs_datas.accumulator.dataU32[44]++; //
            else if (vfd.acout_capacity < 50000.0f)    nvs_datas.accumulator.dataU32[45]++; //
            else if (vfd.acout_capacity < 52000.0f)    nvs_datas.accumulator.dataU32[46]++; //
            else if (vfd.acout_capacity < 54000.0f)    nvs_datas.accumulator.dataU32[47]++; //
            else if (vfd.acout_capacity < 56000.0f)    nvs_datas.accumulator.dataU32[48]++; //
            else if (vfd.acout_capacity < 58000.0f)    nvs_datas.accumulator.dataU32[49]++; //
            else if (vfd.acout_capacity < 60000.0f)    nvs_datas.accumulator.dataU32[50]++; //
            else if (vfd.acout_capacity < 62000.0f)    nvs_datas.accumulator.dataU32[51]++; //
            else if (vfd.acout_capacity < 64000.0f)    nvs_datas.accumulator.dataU32[52]++; //
            else if (vfd.acout_capacity < 66000.0f)    nvs_datas.accumulator.dataU32[53]++; //
            else if (vfd.acout_capacity < 68000.0f)    nvs_datas.accumulator.dataU32[54]++; //
            else if (vfd.acout_capacity < 70000.0f)    nvs_datas.accumulator.dataU32[55]++; //
            else if (vfd.acout_capacity < 72000.0f)    nvs_datas.accumulator.dataU32[56]++; //
            else if (vfd.acout_capacity < 74000.0f)    nvs_datas.accumulator.dataU32[57]++; //
            else if (vfd.acout_capacity < 76000.0f)    nvs_datas.accumulator.dataU32[58]++; //
            else if (vfd.acout_capacity < 78000.0f)    nvs_datas.accumulator.dataU32[59]++; //
            else if (vfd.acout_capacity < 80000.0f)    nvs_datas.accumulator.dataU32[60]++; //
            else if (vfd.acout_capacity >= 80000.0f)   nvs_datas.accumulator.dataU32[61]++; //
            
        }
        else
            vfd.motor_run_sec = 0;
//       else
//        {
//            vfd.emergence_vent_run_cnt = 0;
//            vfd.normal_vent_run_cnt = 0;
//        }

    }

}


/**
  * @brief vfd_in_pin_read  - ?????????????
  * @param
  * @retval
  */
static void vfd_in_pin_read(void)
{
    static uint16_t poweron_cnt = 0;

    // - ???2s????????
    if (poweron_cnt < 0xFFFF)
        poweron_cnt++;


    if (poweron_cnt <= 500)
    {
        // - 1.????????    2.???????10????????????????
        vfd.addr = (vfd.io.add2) + (vfd.io.add1 << 1) ;

        if (nvs_datas.config.serial_com_id != 0)
            vfd.serial_addr = nvs_datas.config.serial_com_id;
        else
            vfd.serial_addr = vfd.addr + MODBUS_ID_OFFSET;

    }

    if (poweron_cnt > 200)   vfd.bit.io_init = 1;

    static uint16_t bAddr1_cnt = 0;
    static uint16_t bAddr2_cnt = 0;

    // - 1.????????    2.???????10????????????????
    vfd.io.add1 = gpio_filter(rt_pin_read(ADDR1_PIN), &bAddr1_cnt, 10 / 2);
    vfd.io.add2 = gpio_filter(rt_pin_read(ADDR2_PIN), &bAddr2_cnt, 10 / 2);

    static uint32_t i1_filter = 0;
    static uint32_t i2_filter = 0;
    static uint32_t i3_filter = 0;
    static uint32_t i4_filter = 0;
    vfd.io.in1 = (vfd.manual.in1_mask & vfd.manual.in1)  || (single_filter(vfd.io.in1, rt_pin_read(IN1_PIN), &i1_filter, 50) & !vfd.manual.in1_mask);
    vfd.io.in2 = (vfd.manual.in2_mask & vfd.manual.in2)  || (single_filter(vfd.io.in2, rt_pin_read(IN2_PIN), &i2_filter, 50) & !vfd.manual.in2_mask);
    vfd.io.in3 = (vfd.manual.in3_mask & vfd.manual.in3)  || (single_filter(vfd.io.in3, rt_pin_read(IN3_PIN), &i3_filter, 50) & !vfd.manual.in3_mask);
    vfd.io.in4 = (vfd.manual.in4_mask & vfd.manual.in4)  || (single_filter(vfd.io.in4, rt_pin_read(IN4_PIN), &i4_filter, 50) & !vfd.manual.in4_mask);

    vfd.io.o1 = rt_pin_read(O1_PIN);
    vfd.io.o2 = rt_pin_read(O2_PIN);
    vfd.io.o3 = rt_pin_read(O3_PIN);

    vfd.io.pow          = DIO_READ_BIT(POW_PIN)  ;
    vfd.io.CLR_HD       = DIO_READ_BIT(CLR_HD_PIN);

    static uint32_t fhd_filter = 0;
    vfd.io.F_HD         = single_filter(vfd.io.F_HD, DIO_READ_BIT(F_HD_PIN), &fhd_filter, 2);
    vfd.io.FS_DR        = DIO_READ_BIT(FS_DR_PIN);

    vfd.io.FS_DR_B      = DIO_READ_BIT(FS_DR_B_PIN);
    vfd.io.ICP          = !DIO_READ_BIT(ICP_PIN) || (DIO_IRQ_DELAY(ICP_PIN) > 0);
    vfd.io.OCP          = !DIO_READ_BIT(OCP_PIN) || (DIO_IRQ_DELAY(OCP_PIN) > 0);

    vfd.io.OVP_P_BUS    = !DIO_READ_BIT(OVP_P_BUS_PIN) || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0);
    vfd.io.F_IPM_INV    = !DIO_READ_BIT(F_IPM_INV_PIN) || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0);
    vfd.io.F_IPM_PFC    = !DIO_READ_BIT(F_IPM_PFC_PIN) || (DIO_IRQ_DELAY(F_IPM_PFC_PIN) > 0);
    vfd.io.pfc_cbc      = !DIO_READ_BIT(F_CBC_PFC_PIN) || (DIO_IRQ_DELAY(F_CBC_PFC_PIN) > 0);
    vfd.io.inv_cbc      = !DIO_READ_BIT(F_CBC_INV_PIN) || (DIO_IRQ_DELAY(F_CBC_INV_PIN) > 0);
    vfd.io.tpsw         = !DIO_READ_BIT(TPSW_PIN)      || (DIO_IRQ_DELAY(TPSW_PIN) > 0);
    vfd.io.kmon_flt     = !DIO_READ_BIT(KMON_FLT_PIN)  || (DIO_IRQ_DELAY(KMON_FLT_PIN) > 0);

    vfd.io.kmon1        = DIO_READ_BIT(KMON1_PIN);

    vfd.ctrl.o1 = (vfd.manual.o1_mask) ? (vfd.manual.o1) : (vfd.ctrl.o1);
    vfd.ctrl.o2 = (vfd.manual.o2_mask) ? (vfd.manual.o2) : (0);
    vfd.ctrl.o3 = (vfd.manual.o3_mask) ? (vfd.manual.o3) : (0);

    if (vfd.manual.kmon1_mask)
        vfd.ctrl.kmon1 = (vfd.manual.kmon1);

    // 4-20mA信号控制启停和转速设置
    uint8_t aux1_start = 0;
    float aux1_ma = vfd.filter_ad.aux1;  // aux1已经转换为mA值

    // 4-20mA启停控制（如果使能）
    if(vfd.aux1_config.start_enable) {
        // 判断启停条件，使用配置的阈值
        static uint32_t aux1_start_filter = 0;
        static uint32_t aux1_stop_filter = 0;

        float start_threshold = vfd.aux1_config.start_threshold / 100.0f;  // 转换为mA
        float stop_threshold = vfd.aux1_config.stop_threshold / 100.0f;    // 转换为mA

        if(aux1_ma >= start_threshold) {
            aux1_start = single_filter(aux1_start, 1, &aux1_start_filter, 100);
        }
        else if(aux1_ma <= stop_threshold) {
            aux1_start = single_filter(aux1_start, 0, &aux1_stop_filter, 100);
        }
    }

    // 4-20mA转速控制（如果使能）
    if(vfd.aux1_config.speed_enable) {
        // 将4-20mA信号映射到转速范围
        float aux1_clamped = (aux1_ma < 4.0f) ? 4.0f : ((aux1_ma > 20.0f) ? 20.0f : aux1_ma);
        float speed_ratio = (aux1_clamped - 4.0f) / 16.0f;  // 4-20mA映射到0-1

        uint16_t target_speed = vfd.aux1_config.speed_min +
                               (uint16_t)(speed_ratio * (vfd.aux1_config.speed_max - vfd.aux1_config.speed_min));

        // 转速转换为频率 (RPM -> 0.01Hz)
        // freq = rpm * ratingFrq / ratingSpeed
        int32_t target_freq = (int32_t)target_speed * nvs_datas.motor.ratingFrq / nvs_datas.motor.ratingSpeed;
        target_freq = (target_freq > 36000) ? 36000 : target_freq;  // 限制最大频率360Hz
        target_freq = (target_freq < 0) ? 0 : target_freq;          // 限制最小频率0Hz

        vfd.ctrl.set_freq = target_freq;
        vfd.ctrl.set_speed = target_speed;
    }

    // 优先级:手动控制 > 4-20mA控制 > IO控制
    if (vfd.manual.start_mask) {
        vfd.ctrl.start = vfd.manual.start;
    } else if (vfd.aux1_config.start_enable) {
        vfd.ctrl.start = aux1_start;
    } else {
        vfd.ctrl.start = vfd.io.in1;
    }

#if (DEBUG_USE_IN123_CMD)
    vfd.ctrl.start_pfc  = (vfd.manual.start_pfc_mask)   ? (vfd.manual.start_pfc)  : (vfd.io.in1);
    vfd.ctrl.start_inv  = (vfd.manual.start_inv_mask)   ? (vfd.manual.start_inv)  : (vfd.io.in3);
#else
    vfd.ctrl.start_pfc  = (vfd.manual.start_pfc_mask) ? (vfd.manual.start_pfc) : (vfd.ctrl.start_pfc);
    vfd.ctrl.start_inv  = (vfd.manual.start_inv_mask) ? (vfd.manual.start_inv) : (vfd.ctrl.start_inv);
#endif

}



/**
  * @brief vfd_out_pin_write  - ??????
  * @param
  * @retval
  */
static void vfd_out_pin_write(void)
{
    static uint8_t _last_kmon3 = 0;
    static uint32_t clr_cnt = 0;
    static uint8_t  clr_bit = 0;

    clr_bit = disactive_filter(clr_bit, vfd.io.F_HD, &clr_cnt, 500);

    rt_pin_write(O1_PIN, vfd.ctrl.o1);
    rt_pin_write(O2_PIN, vfd.ctrl.o2);
    rt_pin_write(O3_PIN, vfd.ctrl.o3);

    rt_pin_write(KMON1_PIN, vfd.ctrl.kmon1);

#ifndef VFD_TEST_DEBUG
    if ((!DIO_READ_BIT(OVP_P_BUS_PIN)  || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0))    ||
            (!DIO_READ_BIT(OCP_PIN)      || (DIO_IRQ_DELAY(OCP_PIN) > 0))            ||
            (!DIO_READ_BIT(ICP_PIN)      || (DIO_IRQ_DELAY(ICP_PIN) > 0))

       )
    {
        rt_pin_write(CLR_HD_PIN, PIN_HIGH);
    }
    else
    {
        rt_pin_write(CLR_HD_PIN, clr_bit);
    }
#else

    if ((vfd.manual.tim1_u_duty || vfd.manual.tim1_v_duty || vfd.manual.tim1_w_duty)
            || (vfd.manual.tim8_u_duty || vfd.manual.tim8_v_duty || vfd.manual.tim8_w_duty)
            || (vfd.manual.dcdc_u_duty || vfd.manual.dcdc_v_duty || vfd.manual.dcdc_w_duty)
       )
    {
        vfd_fs_set(FS_CHNN4, 1);
        rt_pin_write(CLR_HD_PIN, PIN_LOW);
    }
    else
    {
        vfd_fs_set(FS_CHNN4, 0);
        if ((!DIO_READ_BIT(OVP_P_BUS_PIN)  || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0)) ||
                (!DIO_READ_BIT(OCP_PIN)      || (DIO_IRQ_DELAY(OCP_PIN) > 0))     ||
                (!DIO_READ_BIT(ICP_PIN)      || (DIO_IRQ_DELAY(ICP_PIN) > 0))    
           )
        {
            rt_pin_write(CLR_HD_PIN, PIN_HIGH);
        }
        else
        {
            if (!DIO_READ_BIT(F_HD_PIN) || (DIO_IRQ_DELAY(F_HD_PIN) > 0))
            {
                rt_pin_write(CLR_HD_PIN, PIN_LOW);
            }
            else
            {
                rt_pin_write(CLR_HD_PIN, PIN_HIGH);
            }
        }
    }

#endif

}

/**
  * @brief vfd_clear_diagstopcnter  - ????????5s?????????????????
  * @param
  * @retval
  */
static void vfd_clear_diagstopcnter(void)
{
    static uint32_t clear_cnter = 0;

    if (INVERTER_IS_RUN)
    {
        if (clear_cnter <= 60 * 100)
            clear_cnter++;
        else
            vfd.diag_stop_times = 0;
    }
    else
        clear_cnter = 0;
}

void vbus_kmon1_set(uint8_t chnn, uint8_t active)
{
    static uint8_t pfc_vbus_kmon1 = 0;
    static uint8_t dc_vbus_kmon1 = 0;
    static uint8_t inv_vbus_kmon1 = 0;

    switch (chnn)
    {
    case KMONx_CHNN1:
        dc_vbus_kmon1 = active;
        break;
    case KMONx_CHNN2:
        pfc_vbus_kmon1 = active;
        break;
    case KMONx_CHNN3:
        inv_vbus_kmon1 = active;
        break;
    }

    vfd.ctrl.kmon1 = (vfd.manual.kmon1_mask) ? vfd.manual.kmon1 : (dc_vbus_kmon1 || pfc_vbus_kmon1 || inv_vbus_kmon1);

}



#include <rtthread.h>
#include "msh.h"

void cmd_vfd(int argc, char **argv)
{
    rt_kprintf("\r\n");
    if (argc >= 3)
    {
        int val = atoi(argv[2]);

        if (!rt_strcmp("fan", argv[1]))
        {
            if (val > 0)
                vfd_start_fan(val, NULL);
            else
                vfd_stop_fan();
        }
        else if (!rt_strcmp("kmon1", argv[1]))
        {
            vfd.manual.kmon1_mask = (val <= 1) ? 1 : 0;
            vfd.manual.kmon1 = val;
        }
        else if (!rt_strcmp("kmon2", argv[1]))
        {
            vfd.manual.kmon2_mask = (val <= 1) ? 1 : 0;
            vfd.manual.kmon2 = val;
        }
        else if (!rt_memcmp("inverter", argv[1], 3))
        {

            vfd.manual.start_inv_mask = (val <= 1) ? 1 : 0;

            LOG_W("kmon3[%d] kmon2[%d]", vfd.ctrl.kmon3, vfd.ctrl.kmon2);

            vfd.manual.start_inv = val;
        }
        else if (!rt_strcmp("pfc", argv[1]))
        {
            vfd.manual.start_pfc_mask = (val <= 1) ? 1 : 0;

            vfd.manual.start_pfc = val;
        }
        else if (!rt_strcmp("boost", argv[1]))
        {
            vfd.manual.start_dcdc_mask = (val <= 1) ? 1 : 0;
            vfd.manual.start_dcdc = val;
        }
        else if (!rt_strcmp("disp", argv[1]))
        {
            int val = atoi(argv[2]);
            vfd.disp_ctrl = val;
            if (val == 0)
                toolbox_resume_rtkprintf();

            if (argc >= 4)
            {
                int val = atoi(argv[3]);
#ifdef DEBUG_SERIAL_PLOT
                vfd.debug_data_index = val;
#endif
            }
        }
        else
        {
            return;
        }

        rt_kprintf("vfd set %s to %d\r\n", argv[1], val);
    }



}
MSH_CMD_EXPORT_ALIAS(cmd_vfd, vfd, debug);


