#ifndef __UAPP_H__
#define __UAPP_H__

#include "stm32g4xx.h"
#include "rtthread.h"
#include <rtdevice.h>
#include <rthw.h>
#include "stm32g4xx_ll_tim.h"
#include "math.h"
#include "crc.h"
#include "stdlib.h"
#include "drv_io.h"
#include "alg_dio_edge.h"
#include "alg_toolbox.h"
#include "app_ad.h"

#include "app_pfcctrl.h"
#include "user_mcsdk_task.h"
#include "customer_port.h"

extern uint32_t _kernel_v;

#define VFD_SOFT_VERSION 	(_kernel_v & 0xFFFF)
#define VFD_SOFT_MAIN       (_kernel_v / 10000 % 10)
#define VFD_SOFT_SUB        (_kernel_v / 100 % 100)
#define VFD_SOFT_FIXUP      (_kernel_v % 100)
//-------------------------------------------------------------
// 变频器额定参数定义
//-------------------------------------------------------------
#define VFD_RATED_POWER     40000   // 额定功率40kW
#define VFD_MAX_POWER       47000   // 最大功率47kW
#define VFD_RATED_VOLTAGE   380     // 额定电压380V
#define VFD_RATED_CURRENT   99.3f   // 额定电流99.3A
#define VFD_PRODUCT_TYPE    20      // 机型型号

//-------------------------------------------------------------
// 系统状态机
//-------------------------------------------------------------
typedef enum
{
  ST_IDLE       = 0,    /*!<  */
  ST_STOP       = 1,    /*!<  */
  ST_START      = 2,    /*!<  */
  ST_PRECHARGE  = 3,    /*!<  */
  ST_SOFTUP     = 4,    /*!<  */
  ST_RUN        = 5,    /*!<  */
  ST_ERROR      = 6,    /*!<  */
  ST_ERROR_LOCK = 7,
  
} SYS_StateTypeDef;


/* @note test code debug under  VFD_TEST_DEBUG  */
//#define VFD_TEST_DEBUG

/* @note all test code should  compile with #ifdef/#ifndef   */
/*       remember to undefine VFD_TEST_DEBUG when release */
#ifdef VFD_TEST_DEBUG

#define BOARD_USE_WATCH_DOG         (1)
#define DEBUG_USE_IN123_CMD         (0)
#define DEBUG_USE_WIFI_CONSOLE      (0)

#if(DEBUG_USE_WIFI_CONSOLE)

#define DEBUG_WIFI_UART_CHANGE_BAUD

#ifdef DEBUG_WIFI_UART_CHANGE_BAUD
#define DEBUG_UART5_PASSTHROUGH_BAUD    380400
#endif

#endif // DEBUG_USE_WIFI_CONSOLE


#else

#define BOARD_USE_WATCH_DOG     (1)
#endif // VFD_TEST_DEBUG

#define CCMRAM  __attribute__((section("ccmram")))

       
#define VFD_SERIAL_BASE_ADDR        (0x0)

#define DEBUG_REMOTE_IP    "*************" // PC set ip
#define DEBUG_UDP_PORT      50000

#define NOR_FLASH_USE_SPIx      "spi3"
#define NOR_FLASH_CS_PORT       GPIOC//GPIOC
#define NOR_FLASH_CS_PIN        GPIO_PIN_9//GPIO_PIN_9


#define VFD_ENABLE           ( vfd.io.in1 || vfd.ctrl.start_pfc || vfd.ctrl.start_inv)
#define VFD_SWITCH_AC380     (1)
#define VFD_SWITCH_DCDC      (0)
#define VFD_ENABLE_AC380     ((vfd.io.in1||vfd.ctrl.start) && VFD_SWITCH_AC380)
#define VFD_ENABLE_DCDC      ((vfd.io.in1||vfd.ctrl.start) && VFD_SWITCH_DCDC)

#define FHD_ERROR            (!rt_pin_read(F_HD_PIN)  || (DIO_IRQ_DELAY(F_HD_PIN) > 0))
////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////
#define VFD_FAN_FREQ         (350000) /* 350k hz*/

#define VFD_USING_BOOT 

#ifdef VFD_USING_BOOT
#define VFD_VECT_TAB_OFFSET  0x0001C000U /*< boot start from 0x801C000 */
#else
#define VFD_VECT_TAB_OFFSET  0x00000000U /*< boot start from 0x8000000 */
#endif

#define PARAM_FIXUP_NUM       (1)

#define CAN_PTU_PASSTHROUGH_ID  0x00505455

typedef struct
{
    uint32_t timeout_cnt;
    uint32_t last_rx_tick;
    uint16_t since_last_rx_tick;
    uint16_t timeout_set;
    uint16_t rx_cnt;
    uint16_t tx_cnt;
    
    uint8_t  flag_normal  : 1;
    uint8_t  flag_timeout : 1;
    uint8_t  flag_led     : 1;
} com_t;

typedef struct
{
    uint8_t F_HD_flag               : 1;
    uint8_t f_hrtim_breakin_flag    : 1;
    uint8_t igbt_f_mcu1_flag        : 1;
    uint8_t f_tim1_breakin_flag     : 1;
    uint8_t f_tim8_breakin_flag     : 1;
    uint8_t fo1_flag                : 1;
    uint8_t fo2_flag                : 1;
    uint8_t fo3_flag                : 1;
    
    uint8_t ICP_P_flag                : 1;
    uint8_t OCP_N_flag                : 1;
    uint8_t OCP_P_flag                : 1;
    uint8_t OVP_P_BUS_flag            : 1;
    uint8_t F_IPM_INV_flag            : 1;
    uint8_t F_IPM_PFC_flag            : 1;
    uint8_t F_IPM_DC_flag             : 1;
    
    uint8_t POW_flag             : 1;
    
    uint8_t ocp_n_cnt       ;
    uint8_t ocp_p_cnt       ;
    uint8_t igbt_f_mcu1_cnt ;
    uint8_t f_ipm_inv_cnt   ;
    uint8_t f_ipm_pfc_cnt   ;
    uint8_t f_ipm_boost_cnt   ;
    uint8_t fo1_cnt ;
    uint8_t fo2_cnt ;
    uint8_t fo3_cnt ;
} irq_t;

typedef struct
{
    uint8_t dc_00 : 1;
    uint8_t dc_01 : 1;
    uint8_t dc_02 : 1;
    uint8_t dc_03 : 1;
    uint8_t dc_04 : 1;
    uint8_t dc_05 : 1;
    uint8_t dc_06 : 1;
    uint8_t dc_07 : 1;
    uint8_t dc_08 : 1;
    uint8_t dc_09 : 1;
    uint8_t dc_10 : 1;
    uint8_t dc_11 : 1;
    uint8_t dc_12 : 1;
    uint8_t dc_13 : 1;
    uint8_t dc_14 : 1;
    uint8_t dc_15 : 1;
    uint8_t dc_16 : 1;
    uint8_t dc_17 : 1;
    uint8_t dc_18 : 1;
    uint8_t dc_19 : 1;
    uint8_t dc_20 : 1;
    uint8_t dc_21 : 1;
    uint8_t dc_22 : 1;
    uint8_t dc_23 : 1;
    uint8_t dc_24 : 1;
    uint8_t dc_25 : 1;
    uint8_t dc_26 : 1;
    uint8_t dc_27 : 1;
    uint8_t dc_28 : 1;
    uint8_t dc_29 : 1;
    uint8_t dc_30 : 1;
    uint8_t dc_31 : 1;
    
    uint8_t dc_32 : 1;
    uint8_t dc_33 : 1;
    uint8_t dc_34 : 1;
    uint8_t dc_35 : 1;
    uint8_t dc_36 : 1;
    uint8_t dc_37 : 1;
    uint8_t dc_38 : 1;
    uint8_t dc_39 : 1;
    uint8_t dc_40 : 1;
    uint8_t dc_41 : 1;
    uint8_t dc_42 : 1;
    uint8_t dc_43 : 1;
    uint8_t dc_44 : 1;
    uint8_t dc_45 : 1;
    uint8_t dc_46 : 1;
    uint8_t dc_47 : 1;
    uint8_t dc_48 : 1;
    uint8_t dc_49 : 1;
    uint8_t dc_50 : 1;
    uint8_t dc_51 : 1;
    uint8_t dc_52 : 1;
    uint8_t dc_53 : 1;
    uint8_t dc_54 : 1;
    uint8_t dc_55 : 1;
    uint8_t dc_56 : 1;
    uint8_t dc_57 : 1;
    uint8_t dc_58 : 1;
    uint8_t dc_59 : 1;
    uint8_t dc_60 : 1;
    uint8_t dc_61 : 1;
    uint8_t dc_62 : 1;
    uint8_t dc_63 : 1;
    
    uint8_t dc_64 : 1;
    uint8_t dc_65 : 1;
    uint8_t dc_66 : 1;
    uint8_t dc_67 : 1;
    uint8_t dc_68 : 1;
    uint8_t dc_69 : 1;
    uint8_t dc_70 : 1;
    uint8_t dc_71 : 1;
    uint8_t dc_72 : 1;
    uint8_t dc_73 : 1;
    uint8_t dc_74 : 1;
    uint8_t dc_75 : 1;
    uint8_t dc_76 : 1;
    uint8_t dc_77 : 1;
    uint8_t dc_78 : 1;
    uint8_t dc_79 : 1;
    uint8_t dc_80 : 1;
    uint8_t dc_81 : 1;
    uint8_t dc_82 : 1;
    uint8_t dc_83 : 1;
    uint8_t dc_84 : 1;
    uint8_t dc_85 : 1;
    uint8_t dc_86 : 1;
    uint8_t dc_87 : 1;
    uint8_t dc_88 : 1;
    uint8_t dc_89 : 1;
    uint8_t dc_90 : 1;
    uint8_t dc_91 : 1;
    uint8_t dc_92 : 1;
    uint8_t dc_93 : 1;
    uint8_t dc_94 : 1;
    uint8_t dc_95 : 1;
    
    uint8_t dc_96 : 1;
    uint8_t dc_97 : 1;
    uint8_t dc_98 : 1;
    uint8_t dc_99 : 1;
    uint8_t dc_100 : 1;
    uint8_t dc_101 : 1;
    uint8_t dc_102 : 1;
    uint8_t dc_103 : 1;
    uint8_t dc_104 : 1;
    uint8_t dc_105 : 1;
    uint8_t dc_106 : 1;
    uint8_t dc_107 : 1;
    uint8_t dc_108 : 1;
    uint8_t dc_109 : 1;
    uint8_t dc_110 : 1;
    uint8_t dc_111 : 1;
    uint8_t dc_112 : 1;
    uint8_t dc_113 : 1;
    uint8_t dc_114 : 1;
    uint8_t dc_115 : 1;
    uint8_t dc_116 : 1;
    uint8_t dc_117 : 1;
    uint8_t dc_118 : 1;
    uint8_t dc_119 : 1;
    uint8_t dc_120 : 1;
    uint8_t dc_121 : 1;
    uint8_t dc_122 : 1;
    uint8_t dc_123 : 1;
    uint8_t dc_124 : 1;
    uint8_t dc_125 : 1;
    uint8_t dc_126 : 1;
    uint8_t dc_127 : 1;
    
    uint8_t dc_128 : 1;
    uint8_t dc_129 : 1;
    uint8_t dc_130 : 1;
    uint8_t dc_131 : 1;
    uint8_t dc_132 : 1;
    uint8_t dc_133 : 1;
    uint8_t dc_134 : 1;
    uint8_t dc_135 : 1;
    uint8_t dc_136 : 1;
    uint8_t dc_137 : 1;
    uint8_t dc_138 : 1;
    uint8_t dc_139 : 1;
    uint8_t dc_140 : 1;
    uint8_t dc_141 : 1;
    uint8_t dc_142 : 1;
    uint8_t dc_143 : 1;
    uint8_t dc_144 : 1;
    uint8_t dc_145 : 1;
    uint8_t dc_146 : 1;
    uint8_t dc_147 : 1;
    uint8_t dc_148 : 1;
    uint8_t dc_149 : 1;
    uint8_t dc_150 : 1;
    uint8_t dc_151 : 1;
    uint8_t dc_152 : 1;
    uint8_t dc_153 : 1;
    uint8_t dc_154 : 1;
    uint8_t dc_155 : 1;
    uint8_t dc_156 : 1;
    uint8_t dc_157 : 1;
    uint8_t dc_158 : 1;
    uint8_t dc_159 : 1;
    
}DIAG_T;

#define Ox_ON    (0)
#define Ox_OFF   (1)

#define LEV_1 1
#define LEV_2 2
#define LEV_3 3
#define LEV_N 0

#define MSK_DN_TCMS     0x1
#define MSK_DN_REC      0x2
#define MSK_DN_LED      0x4
#define MSK_DISABLE     0x8
#pragma pack(push, 1)
typedef struct{
    uint8_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t min;
    uint8_t sec;
}rtc_date_t;

typedef struct
{
    uint16_t code;

    uint8_t level;    /*!< 0:none 1:lock&stop  2:stop  3:only record   */ 
    uint8_t mask : 3; /*!< bit0: dnot2tcms */
                      /*!< bit1: dnot2record */
                      /*!< bit2: dnot2led */
    
    uint8_t type : 3;  /*!< bit0: software */
                       /*!< bit1: hardware */
                   
    uint32_t happen_cnt;
    uint32_t resume_cnt;
    char *   str;
}diag_t;

typedef struct{
	int16_t  adca[22];
}ad_cal_t;

typedef struct{
	uint16_t ver[16];    //çĄ?äťśçć?
}hw_ver_t;

typedef struct{
	char HwBarCode[9][32];    
}barcode_t;

typedef struct{
	uint8_t can_x_addr;
    uint8_t can_y_addr;
}can_addr_t;

typedef struct{
		uint16_t DAC1_Vol; 		//DAC1电压输出
		uint16_t DAC2_Vol; 		//DAC2电压输出
}dac_t;

typedef struct
{
	char local_ip[16];
    char remote_ip[16];
    char ssid[16];
    char passwd[16];
}wifi_config_t;
	
typedef struct{
	float       dataF32[4];
	uint32_t    dataU32[64];
}record_acc_t;

#define DEFAULT_SERIAL_COM_ID     (0xE4)
#define MODBUS_ID_OFFSET          (0x01)
typedef struct
{
    struct {
    char     dev[8];
    char     serial_num[16];
    char     bar_code[32];
    uint8_t  soft_ver_main;
    uint8_t  soft_ver_sub;
    uint8_t  paramt_fixup_magic;
    uint8_t  soft_ver_test;
    uint8_t  hard_ver;
    uint8_t  fal_ver;    

	ad_cal_t 	   SetAdCa;
	hw_ver_t  	   SetHwVer;
	can_addr_t     SetCanAddr;
    wifi_config_t  SetWifi;
    
    uint32_t  can_baudrate;
    uint32_t  rs485_baudrate;
    uint8_t   rs485_check_bit; 
    uint8_t   serial_com_id;    
    
    uint16_t       crc;
    }config;
    
    record_acc_t   accumulator;
    
    struct{
    barcode_t 	    SerialNo;
    hw_ver_t  	    SetHwVer;
    uint16_t        crc;
    }info;
    
    struct{
    uint16_t reg_3000[20];
    uint16_t reg_4000[20];
    uint16_t crc;
    }modbus;
	
	struct{
     uint16_t param_src;              // F0-00ĺć°çąťĺ 0x5A=č?ĺŽäš 0=çźşçĺ?
    //7
    uint16_t motorCtrlMode;           // F0-01  (çľćş)ć§ĺśćšĺź
    uint16_t runDir;                  // F0-09  čżčĄćšĺ
    uint16_t upperFrq;                // F0-12  ä¸éé˘çć°ĺźč?žĺŽ
    uint16_t lowerFrq;                // F0-14  ä¸éé˘çć°ĺźč?žĺŽ  
    uint16_t carrierFrq;              // F0-15  č˝˝ćł˘é˘ç
    uint16_t accTime1;                // F0-17  ĺ éćśé?1
    uint16_t decTime1;                // F0-18  ĺéćśé?1
    //11    
    uint16_t motorType;               // F1-00  çľćşçąťĺéćŠ
    uint16_t ratingPower;             // F1-01  çľćşé˘ĺŽĺç
    uint16_t ratingVoltage;           // F1-02  çľćşé˘ĺŽçľĺ
    uint16_t ratingCurrent;           // F1-03  çľćşé˘ĺŽçľćľ
    uint16_t ratingFrq;               // F1-04  çľćşé˘ĺŽé˘ç    
    uint16_t ratingSpeed;             // F1-05  çľćşé˘ĺŽč˝?é?    
    uint16_t pmsmRs;                  // F1-16  ĺć?ĽćşĺŽĺ­çľéť
    uint16_t pmsmLd;                  // F1-17  ĺć?Ľćşdč˝´çľć?
    uint16_t pmsmLq;                  // F1-18  ĺć?Ľćşqč˝´çľć?
    uint16_t pmsmCoeff;               // F1-20  ĺć?Ľćşĺçľĺ¨ĺżçłťć° 
    uint16_t F1_37;                   // F1-37  
    //52    
    uint16_t vcSpdLoopKp1;            // F2-00  éĺşŚç?ćŻäžĺ˘ç1
    uint16_t vcSpdLoopTi1;            // F2-01  éĺşŚç?ç§?ĺćśé?1
    uint16_t vcSpdLoopChgFrq1;        // F2-02  ĺć˘é˘ç1
    uint16_t vcSpdLoopKp2;            // F2-03  éĺşŚç?ćŻäžĺ˘ç2
    uint16_t vcSpdLoopTi2;            // F2-04  éĺşŚç?ç§?ĺćśé?2
    uint16_t vcSpdLoopChgFrq2;        // F2-05  
    uint16_t F2_06;                   // F2-06  
    uint16_t F2_07;                   // F2-07  
    uint16_t F2_08;                   // F2-08  
    uint16_t F2_09;                   // F2-09  
    uint16_t spdCtrlDriveTorqueLimit; // F2-10  éĺşŚć§ĺś(éŠąĺ¨)č˝?çŠä¸éć°ĺ­č?žĺŽ
    uint16_t F2_11;                   // F2-11  
    uint16_t F2_12;                   // F2-12  
    uint16_t mAcrKp;                  // F2-13  Mč˝´çľćľçŻKp
    uint16_t mAcrKi;                  // F2-14  Mč˝´çľćľçŻKi
    uint16_t tAcrKp;                  // F2-15  Tč˝´çľćľçŻKp
    uint16_t tAcrKi;                  // F2-16  Tč˝´çľćľçŻKi
    uint16_t F2_17;                   // F2-17  
    uint16_t F2_18;                   // F2-18  
    uint16_t weakFlusCoef;            // F2-19  ĺć?Ľćşĺźąç?çłťć?(ç°ćšä¸?:čżč°ĺśä˝żč˝éćŠ)   
    uint16_t F2_20;                   // F2-18 
    uint16_t F2_21;                   // F2-18
    uint16_t F2_22;                   // F2-18
    uint16_t F2_23;                   // F2-18
    uint16_t F2_24;                   // F2-18
    uint16_t F2_25;                   // F2-18
    uint16_t F2_26;                   // F2-18
    
    uint16_t F2_27;                   // F2-18
    uint16_t F2_28;                   // F2-18
    uint16_t F2_29;                   // F2-18
    uint16_t F2_30;                   // F2-18
    uint16_t F2_31;                   // F2-18
    
    uint16_t F2_32;                   // F2-18
    uint16_t F2_33;                   // F2-18
    uint16_t F2_34;                   // F2-18
    uint16_t F2_35;                   // F2-18
    uint16_t F2_36;                   // F2-18
    uint16_t F2_37;                   // F2-18
    uint16_t F2_38;                   // F2-18
    uint16_t F2_39;                   // F2-18
    uint16_t F2_40;                   // F2-18
    uint16_t F2_41;                   // F2-18
    uint16_t F2_42;                   // F2-18
    uint16_t F2_43;                   // F2-18
    uint16_t F2_44;                   // F2-18
    uint16_t F2_45;                   // F2-18
    uint16_t F2_46;                   // F2-18
    uint16_t F2_47;                   // F2-18
    uint16_t F2_48;                   // F2-18
    uint16_t F2_49;                   // F2-18
    uint16_t F2_50;                   // F2-18
    uint16_t F2_51;                   // F2-18
    //34
    uint16_t F3_00;                   // F3-00
    uint16_t F3_01;                   // F3-00
    uint16_t F3_02;                   // F3-00
    uint16_t F3_03;                   // F3-00
    uint16_t F3_04;                   // F3-00
    uint16_t F3_05;                   // F3-00
    uint16_t F3_06;                   // F3-00
    uint16_t F3_07;                   // F3-00
    uint16_t F3_08;                   // F3-00
    uint16_t F3_09;                   // F3-00
    uint16_t F3_10;                   // F3-00
    uint16_t F3_11;                   // F3-00
    uint16_t F3_12;                   // F3-00
    uint16_t F3_13;                   // F3-00
    uint16_t F3_14;                   // F3-00
    uint16_t F3_15;                   // F3-00
    uint16_t F3_16;                   // F3-00
    uint16_t F3_17;                   // F3-00
    uint16_t F3_18;                   // F3-00
    uint16_t F3_19;                   // F3-00
    uint16_t F3_20;                   // F3-00
    uint16_t F3_21;                   // F3-00
    uint16_t F3_22;                   // F3-00
    uint16_t F3_23;                   // F3-00
    uint16_t F3_24;                   // F3-00
    uint16_t F3_25;                   // F3-00
    uint16_t F3_26;                   // F3-00
    uint16_t F3_27;                   // F3-00
    uint16_t F3_28;                   // F3-00
    uint16_t F3_29;                   // F3-00
    uint16_t F3_30;                   // F3-00
    uint16_t F3_31;                   // F3-00
    uint16_t F3_32;                   // F3-00
    uint16_t F3_33;                   // F3-00
    //23
    uint16_t F6_00;                   // F6-00
    uint16_t F6_01;                   // F6-00
    uint16_t F6_02;                   // F6-00
    uint16_t F6_03;                   // F6-00
    uint16_t F6_04;                   // F6-00
    uint16_t F6_05;                   // F6-00
    uint16_t F6_06;                   // F6-00
    uint16_t F6_07;                   // F6-00
    uint16_t F6_08;                   // F6-00
    uint16_t F6_09;                   // F6-00
    uint16_t stopMode;                // F6-00
    uint16_t F6_11;                   // F6-00
    uint16_t F6_12;                   // F6-00
    uint16_t F6_13;                   // F6-00
    uint16_t F6_14;                   // F6-00
    uint16_t F6_15;                   // F6-00
    uint16_t F6_16;                   // F6-00
    uint16_t F6_17;                   // F6-00
    uint16_t F6_18;                   // F6-00
    uint16_t F6_19;                   // F6-00
    uint16_t F6_20;                   // F6-00
    uint16_t F6_21;                   // F6-00
    uint16_t F6_22;                   // F6-00
    //9
    uint16_t aux1_start_enable;       // F9-00  4-20mA启停控制使能 0:禁用 1:启用
    uint16_t aux1_speed_enable;       // F9-01  4-20mA转速控制使能 0:禁用 1:启用
    uint16_t aux1_speed_min;          // F9-02  4mA对应的最小转速(RPM)
    uint16_t aux1_speed_max;          // F9-03  20mA对应的最大转速(RPM)
    uint16_t aux1_start_threshold;    // F9-04  启动阈值(mA*100) 默认700(7.0mA)
    uint16_t aux1_stop_threshold;     // F9-05  停止阈值(mA*100) 默认500(5.0mA)
    uint16_t F9_06;                   // F9-06  保留
    uint16_t F9_07;                   // F9-07  保留
    uint16_t F9_13;                   // F9-00
    uint16_t F9_48;                   // F9-00
    uint16_t maxFrq;                  // F0-10  ćĺ¤§é˘ç 
    uint16_t BreakinAutomaticOutput;  // FU-01  breakin čŞć˘ĺ¤
    uint16_t crc;    
    }motor;
    
    uint32_t  write_paramt_time;

}nvs_data_t;

typedef struct{
    int16_t    vbus_inv;   
    int16_t    ac_iout_u;
    int16_t    ac_iout_v;
    int16_t    ac_iout_w;
    int16_t    ac_vout;
    int16_t    ac_vout_u;
    int16_t    ac_vout_v;
    int16_t    ac_vout_w;
    int16_t    ac_vin_r;
    int16_t    ac_vin_s;
    int16_t    ac_vin_t;
    int16_t    ac_iin_r;
    int16_t    ac_iin_s;
    int16_t    ac_iin_t;
    int16_t    dc_12V;
    int16_t    dc_24V;
    int16_t    dc_5V;
    int16_t    temp_inv_mos;
    int16_t    temp_pfc_mos;
  
    int16_t    temp_BUS_CAP;
    int16_t    temp_BUS_CAP2;
    int16_t    temp_PFC_L;
    int16_t    temp_EMC_L;
    int16_t    temp_POW_DIODE;      
    int16_t    aux1;
    int16_t    aux2;
    int16_t    temp_hdc1080;    
    int16_t    mosi_hdc1080;    
    int16_t    dcVin;

    // no match
    // int16_t    dc_15V;          
    // int16_t    temp_DC_L; 
    // int16_t    temp_DC_C;      
    // int16_t    temp_DC_HB;      
    // int16_t    temp_mcu;       
    // int16_t    dc_vinbus;
    // int16_t    dc_Iin;
    // int16_t    llc_IBin;
    // int16_t    temp_DC_T;
    // int16_t    temp_DCL;
    // int16_t    temp_ACL;  
}AD_T;

typedef struct{
    float    vbus_inv;   
    float    ac_iout_u;
    float    ac_iout_v;
    float    ac_iout_w;
    float    ac_vout;
    float    ac_vout_u;
    float    ac_vout_v;
    float    ac_vout_w;
    float    ac_vin_r;
    float    ac_vin_s;
    float    ac_vin_t;
    float    ac_iin_r;
    float    ac_iin_s;
    float    ac_iin_t;
    float    dc_12V;
    float    dc_24V;
    float    dc_5V;
    float    temp_inv_mos;
    float    temp_pfc_mos;
  
    float    temp_BUS_CAP;
    float    temp_BUS_CAP2;
    float    temp_PFC_L;
    float    temp_EMC_L;
    float    temp_POW_DIODE;      
    float    aux1;
    float    aux2;
    float    temp_hdc1080;    
    float    mosi_hdc1080;    
    float    dcVin;
     // no match
    // float    dc_15V;          
    // float    temp_DC_L; 
    // float    temp_DC_C;      
    // float    temp_DC_HB;      
    // float    temp_mcu;       
    // float    dc_vinbus;
    // float    dc_Iin;
    // float    llc_IBin;
    // float    temp_DC_T;
    // float    temp_DCL;
    // float    temp_ACL;  
}FAD_T;

typedef struct{
    uint16_t   dac1_inv;
    uint16_t   dac2_inv;
    uint16_t   dac1_pfc;
    uint16_t   dac2_pfc;
}DA_T;

typedef struct{
    uint8_t   hard_stop         : 1; 
    uint8_t   soft_stop         : 1;
    uint8_t   diag_lvl_1        : 1;
    uint8_t   diag_lvl_2        : 1;
    uint8_t   diag_lvl_3        : 1;
    uint8_t   diag_soft         : 1;
    uint8_t   diag_hard         : 1;
    uint8_t   freq_err_flag     : 1;
    
    uint8_t   diag_pfc_lvl_2    : 1;
    uint8_t   lock_stop         : 1;
    uint8_t   diag_pfc_soft     : 1;
    uint8_t   diag_pfc_hard     : 1;
    uint8_t   com_485           : 1;
    uint8_t   com_can           : 1;
    uint8_t   com_ptu           : 1;
    uint8_t   dio_init	        : 1;
                                
    uint8_t   com_wifi		    : 1;
    uint8_t   nvs_datas_init    : 1;
    uint8_t   mount_fs          : 1;
    uint8_t   cmd_start_pfc     : 1;
    uint8_t   cmd_start_vfd     : 1;
    uint8_t   pfc_wdg_timeout   : 1;
    uint8_t   io_init           : 1;
    uint8_t   rtc_init          : 1;
                                
    uint8_t   vbus_ready        : 1;
    uint8_t   vinbus_ready      : 1;
    uint8_t   kmon1_actived     : 1;
    uint8_t   kmon2_actived     : 1;
    uint8_t   vac_ready         : 1;
    uint8_t   freq_ready        : 1;
    uint8_t   freq_revert       : 1;
    uint8_t   precharge_ready   : 1;
    
    uint8_t   inv_ready         : 1;
    uint8_t   stop_ready        : 1;
    uint8_t   spiflash_init     : 1;
    uint8_t   ad_init           : 1;
    uint8_t   isr_freq_ready    : 1;
    uint8_t   rtc_sync          : 1;
    uint8_t   pow_init          : 1;
    uint8_t   system_init       : 1;
    
    uint8_t   pfc_break_flag    : 1;
    uint8_t   inv_break_flag    : 1;
    uint8_t   dcdc_break_flag   : 1;
    uint8_t   in_stop_ready     : 1;
    uint8_t   kmon3_actived     : 1;
    uint8_t   acvin_low_stop    : 1;
    uint8_t   dcvin_low_stop    : 1;
    uint8_t   start_timeout     : 1;
    
    uint8_t   nvs_reg_read      : 1;
    uint8_t   nvs_info_read     : 1;
    uint8_t   nvs_config_read   : 1;
    uint8_t   nvs_acc_read      : 1;
    uint8_t   close_detect     : 1;
    uint8_t   novolt_warning_flag         : 1;


    uint8_t   res03             ;
}BIT_T;

typedef struct{
    uint8_t    in1 		: 1 ;
    uint8_t    in2 		: 1 ;
    uint8_t    in3 		: 1 ;
    uint8_t    o1 		: 1 ;
    uint8_t    o2 		: 1 ;
    uint8_t    o3 		: 1 ;
    uint8_t    res01    : 1 ;
    uint8_t    tpsw 	: 1 ;
    
    uint8_t    kmon_flt : 1 ; 
    uint8_t    kmon1    : 1 ;
    uint8_t    kmon2    : 1 ;
    uint8_t    pow		: 1 ;
    uint8_t    CLR_HD	: 1 ;  
    uint8_t    F_HD	    : 1 ;
    uint8_t    FS_DR	: 1 ;
    uint8_t    FS_DR_B	: 1 ; 
    
    uint8_t    add1	    : 1 ;
    uint8_t    add2	    : 1 ;
    uint8_t    ICP  	: 1 ;
    uint8_t    OCP  	: 1 ;
    uint8_t    pfc_cbc  : 1 ;
    uint8_t    in4	    : 1 ;
    uint8_t    inv_cbc	: 1 ;
	uint8_t    kmon3    : 1 ;
    
    uint8_t    OVP_P_BUS	: 1 ;
    uint8_t    F_IPM_INV	: 1 ;
    uint8_t    F_IPM_PFC	: 1 ;
    uint8_t    F_IPM_DC	    : 1 ;
    uint8_t    cbc_flag : 1 ;
    uint8_t    rev_overrpm_flag : 1;
    uint8_t    res04	    : 4 ;

    
}IO_T;

enum{
	INV_MODE_STOP = 0, // 0
	INV_MODE_RUN,// 1
    INV_MODE_ERR,// 2
    INV_MODE_OUTPUT_LIMIT,// 3
    INV_MODE_INTPUT_LIMIT,// 4
    INV_MODE_TEMP_LIMIT,// 5
    INV_MODE_FAN_LIMIT,// 6
    INV_MODE_ERR_LOCK,// 7
    INV_MODE_ERR_STOP,// 8
    INV_MODE_EMERG_RUN,// 9
    INV_MODE_OVER_TEMP_1,//10
    INV_MODE_OVER_TEMP_2,//11
    INV_MODE_OVER_TEMP_3,//12
    INV_MODE_OVER_TEMP_4,//13
    INV_MODE_5V,// 14
};

#define FS_CHNN1        0
#define FS_CHNN2        1
#define FS_CHNN3        2
#define FS_CHNN4        3

#define KMONx_CHNN1     0
#define KMONx_CHNN2     1
#define KMONx_CHNN3     2

typedef struct{
    
    uint8_t    sys_st;      //log offset 53
    uint8_t    inv_st;
    uint8_t    pfc_st;
    uint8_t    llc_st;
    
    uint16_t   freq_out;
    int32_t    freq_ref;
    int16_t    motor_speed;
    int16_t    set_freq;
    uint16_t   vbus_ref;
    int16_t    set_speed;
    int16_t    motor_freq;
    
    uint8_t    sys_mode;

    uint8_t    start              : 1;
    uint8_t    start_pfc          : 1;
    uint8_t    start_dcdc         : 1;
    uint8_t    start_inv          : 1;
    uint8_t    o1                 : 1;
    uint8_t    o2                 : 1;
    uint8_t    o3                 : 1;
    uint8_t    kmon1              : 1;
                                      
    uint8_t    kmon2              : 1; 
    uint8_t    fs_pfc             : 1;
    uint8_t    fs_boost           : 1;
    uint8_t    erase_flash        : 1;
    uint8_t    load_reducing      : 1;
    uint8_t    cmd_speed_mode     : 1;
    uint8_t    cmd_vf_mode        : 1;
    uint8_t    cmd_auto_mode      : 1;
    
    uint8_t    fs_inv             : 1;
    uint8_t    kmon3              : 1;
    uint8_t    start_pfc_invmode  : 1;
    uint8_t    res01              : 5;
    
    
}CTRL_T;


typedef struct
{
    uint8_t    enable_magic         : 4 ;
    uint8_t    start                : 1 ;
    uint8_t    start_mask           : 1 ;
    uint8_t    start_pfc            : 1 ;
    uint8_t    start_pfc_mask       : 1 ;
    
    uint8_t    start_inv            : 1 ;
    uint8_t    start_inv_mask       : 1 ;
    uint8_t    start_dcdc          : 1 ;
    uint8_t    start_dcdc_mask     : 1 ;
    uint8_t    in1                  : 1 ;
    uint8_t    in1_mask             : 1 ;
    uint8_t    in2                  : 1 ;
    uint8_t    in2_mask             : 1 ;
    
    uint8_t    in3                  : 1 ;
    uint8_t    in3_mask             : 1 ;
    uint8_t    in4                  : 1 ;
    uint8_t    in4_mask             : 1 ;
    uint8_t    o1                   : 1 ;
    uint8_t    o1_mask              : 1 ;
    uint8_t    o2                   : 1 ;
    uint8_t    o2_mask              : 1 ;
    
    uint8_t    o3                   : 1 ;
    uint8_t    o3_mask              : 1 ;
    uint8_t    kmon1                : 1 ;
    uint8_t    kmon1_mask           : 1 ;
    uint8_t    kmon2                : 1 ;
    uint8_t    kmon2_mask           : 1 ;
    uint8_t    kmon3                : 1 ;
    uint8_t    kmon3_mask           : 1 ;
    
    
    uint8_t    dcdc_u_duty;
    uint8_t    dcdc_v_duty;
    uint8_t    dcdc_w_duty;
    
    uint32_t    dcdc_freq;
    
    uint8_t    llc_vset_mask : 1;
    uint8_t    pfc_vset_mask : 1;
    uint8_t    tim1_mask : 1;
    uint8_t    tim8_mask : 1;
    uint8_t    timx_Fan_mask : 1;
    uint8_t    tim1_u_duty;
    uint8_t    tim1_v_duty;
    uint8_t    tim1_w_duty;
    
    
    uint8_t    tim8_u_duty;
    uint8_t    tim8_v_duty;
    uint8_t    tim8_w_duty;
    
    uint8_t    timx_fan_duty;        //风扇占空比
    
}MANUAL_T;

//#define DEBUG_SERIAL_PLOT
#define DEBUG_PLOT_POINTS   200


typedef struct 
{
  /*byte 00 */ rtc_date_t  date;            /* PTU XML OFFSET="013" */
  /*byte 06 */ uint8_t  res06;             
  /*byte 07 */ uint8_t  event_code;
  /*byte 08 */ uint8_t  diag_code;
  
  /*byte 09 */ uint8_t  diag_active : 1;
  /*byte 09 */ uint8_t  diag_resume : 1;
  /*byte 09 */ uint8_t  io_rising   : 1;
  /*byte 09 */ uint8_t  io_falling  : 1;
  /*byte 09 */ uint8_t  res09       : 4;
  
}log_head_t;

typedef struct 
{       
   uint8_t  data[118];
    
  /*byte 128 END */ 
}log_data_t;

typedef struct 
{
  log_head_t  head;
  log_data_t  data;  
    
}record_log_t;

#pragma pack(pop)

struct rt_powersupply_ops {
	rt_err_t ( *init )( void );
	rt_err_t ( *start )( void );
	rt_err_t ( *stop )( void );
	rt_err_t ( *reset )( void );
	rt_err_t ( *control )( int cmd, void *arg );
	rt_err_t ( *machine_task )( void );
};

typedef struct{
    uint32_t        version;
    uint32_t        board_id;
    rtc_date_t      date;   
    uint8_t         code[4];
    uint8_t         soft_ver_main;
    uint8_t         soft_ver_sub;
    uint8_t         paramt_fixup_magic;
    uint8_t         soft_ver_test;
    uint16_t        ctrl_hw_ver;   
    uint8_t         product_type;    
    uint8_t         addr;
    uint8_t         can_addr;
    uint8_t         serial_addr;
    uint8_t         modbus_addr;
    uint8_t         diag_latch;
    
    FAD_T           fast_ad;
    FAD_T           filter_ad;
    //AD_T            analog2digital;
    AD_T            analog;
    DA_T            da;
    IO_T            io;
    CTRL_T          ctrl;
    DIAG_T          diag;
    BIT_T           bit;
    MANUAL_T        manual;
    
    float           acin_power;
    float           acout_power;
    float           powerconsumption_now;
    float           dc_powerconsumption_now;
    uint32_t        total_run_time;
    uint8_t         diag_history[5];
    uint8_t         diag_stop_times; 

    uint32_t        use_tick;
    uint8_t         log_size;
	uint8_t         is_update_fw;
	uint8_t         fan_duty;
	uint8_t    	    nvs_write_config_flag;
	ad_cal_t 	    SetAdCa;
	hw_ver_t  	    SetHwVer;
	can_addr_t      SetCanAddr;
    
    rt_pfc_t        * const pfc;
    rt_mcsdk_t      * const inverter;
    
    uint8_t         stop_all_pwm_flag : 1;
    uint8_t         flyingStartFlag   : 1;
    uint8_t         dcVin_low_Flag    : 1;
    uint8_t         acVin_low_Flag    : 1;
    
    float           flyingCalFreq;
    
    
    uint32_t        temp;
    uint8_t         disp_ctrl;

    uint8_t         test_pfc_cmd;
    uint8_t         rs485_baudswtich_cnter;
    uint8_t         res;
    
    int16_t         motor_speed;

    float           abs_acin_freq;
    float           abs_fast_acin_freq;
    
    float           dcdc_power;
    float           acin_capacity;
    float           acout_capacity;
    
    float           ac_rated_power;
    float           dc_rated_power;
    float           acin_freq;
    float           acin_freq_folp;
    float           acin_absfreq;
	
	float           acout_freq;
    float           acout_freq_folp;
    float           acout_absfreq;
    float           coeff_acin_powerfactor;
    float           coeff_acout_powerfactor;
    
    float           coeff_normal_acin_overload;
    float           coeff_normal_acout_overload;
    float           coeff_emergnc_dcin_overload;
    float           coeff_emergnc_acout_overload;
    
    float           normal_acin_current;
    float           normal_acout_current;
    float           emergnc_acout_current;
    float           emergnc_dcin_current;
    
    float           pfc_invmode_IdRef;
    float           pfc_IdRef;
    
    uint16_t        modbus_rx_out_cnter;
    uint16_t        ptu_rx_cnter;
    uint16_t        modbus_rx_cnter;
    uint16_t        modbus_tx_cnter;
    uint16_t        modbus_tx_err_cnter;
    uint16_t        modbus_rx_crc_err_cnter;
    uint16_t        modbus_rx_add_err_cnter;
    uint16_t        modbus_rx_other_err_cnter;
    uint16_t        modbus_tx_irq_cnter;
    uint16_t        rs485_rx_cnter;
    
    PID_t           pid_acin_power;
    PID_t           pid_acout_power;
    PID_t           pid_dcin_power;
    PID_t           pid_dcin_i;
    uint8_t         usart2_default_flag;
    uint8_t         diag_code;
    uint32_t        read_kv_cnter ;   
    uint8_t         modbus_id_lock;     
    
    uint32_t        normal_vent_run_cnt;
    uint32_t        emergence_vent_run_cnt;
    uint8_t         precharge_kmon3_try;
    uint8_t         freq_switch_cnt;
    uint8_t         freq_err_cnt;
	uint16_t        debug_code16;
    int             over_modulation_cnt;
    uint16_t        invc_err_code;
    uint32_t        motor_run_sec;
    float           deta_vbus;
    float           deta_vbus_filt;
    float           dcIinMax;
    float           dcIinCoeff;
    uint8_t         test_code;
	uint8_t         driveMotorType;
	uint8_t         voltInputType;
    uint32_t        startDC_SecTick;
	float 			startDC_Cur;
	uint8_t 		cpu_major;
	uint8_t 		cpu_minor;
    int8_t 		    dcvin_index;
}vfd_t;

extern AC_Phase_T acvin_r,acvin_s,acvin_t;
extern AC_Phase_T aciin_r,aciin_s,aciin_t;
extern AC_Phase_T aciout_u,aciout_v,aciout_w;
extern AC_Phase_T acvout_u,acvout_v,acvout_w;
#define DC110V_Is_Ok            ((vfd.filter_ad.dc_vinbus > DC_VIN_LOW) && (vfd.filter_ad.dc_vinbus < DC_VIN_OVER))
#define DC5V_Is_Ok              ((vfd.filter_ad.dc_5V > DC5V_LOW) && (vfd.filter_ad.dc_5V < DC5V_OVER))
#define DC15V_Is_Ok             ((vfd.filter_ad.dc_12V > DC15V_LOW) && (vfd.filter_ad.dc_5V < DC15V_OVER))
#define AC380_FreqIs_Ok         ((vfd.abs_acin_freq >= ACIN_FREQ_LOW) && (vfd.abs_acin_freq <= ACIN_FREQ_OVER))
#define AC380V_Is_Ok            ((vfd.filter_ad.ac_vin_r >= AC_VIN_VALID) && (vfd.filter_ad.ac_vin_s >= AC_VIN_VALID) && (vfd.filter_ad.ac_vin_t >= AC_VIN_VALID))
#define DC15V_5V_Is_Ok          (DC5V_Is_Ok && DC15V_Is_Ok)

#define Fast_DC110V_Is_Ok       (0)
#define Fast_DC5V_Is_Ok         ((vfd.fast_ad.dc_5V > DC5V_LOW) && (vfd.fast_ad.dc_5V < DC5V_OVER))
#define Fast_DC15V_Is_Ok        ((vfd.fast_ad.dc_12V > DC15V_LOW) && (vfd.fast_ad.dc_5V < DC15V_OVER))
#define Fast_AC380_FreqIs_Ok    ((vfd.abs_fast_acin_freq >= ACIN_FREQ_LOW) && (vfd.abs_fast_acin_freq <= ACIN_FREQ_OVER))
#define Fast_AC380V_Is_Ok       ((vfd.fast_ad.ac_vin_r > AC_VIN_VALID) && (vfd.fast_ad.ac_vin_s > AC_VIN_VALID) && (vfd.fast_ad.ac_vin_t > AC_VIN_VALID))
#define Fast_DC15V_5V_Is_Ok     (Fast_DC5V_Is_Ok && Fast_DC15V_Is_Ok)

#define VFD_DC110_VALID         (0)
#define VFD_AC380_VALID         (Fast_AC380_FreqIs_Ok && Fast_AC380V_Is_Ok)

#ifdef VFD_TEST_DEBUG
#define VFD_VIN_OK               (1)
#else
#define VFD_VIN_OK              ((VFD_SWITCH_AC380&&VFD_AC380_VALID))    
#endif
#define INVERTER_IS_RUN         (vfd.ctrl.inv_st == ST_RUN)
#define PFC_IS_RUN              (ST_RUN == vfd.ctrl.pfc_st)
#define DCDC_IS_RUN             ((ST_PRECHARGE == vfd.ctrl.llc_st) || (ST_RUN == vfd.ctrl.llc_st) || (ST_SOFTUP == vfd.ctrl.llc_st))
#define PWM_IS_RUN              (INVERTER_IS_RUN || PFC_IS_RUN || DCDC_IS_RUN)

#define NVS_MOTOR_USER              (0x5A)
#define NVS_MOTOR_FUNCCODE          (0x00)
#define NVS_MOTOR_TYC90_LD_YS90_1L  (0x01)
#define NVS_MOTOR_TYC80_LS_YS80_5   (0x02)
#define NVS_MOTOR_TYC90_LD_YS90_1R  (0x03)
#define NVS_MOTOR_ACIM_VF           (0x04)
#define NVS_MOTOR_AXIAL_FAN         (0x05)
#define NVS_MOTOR_ZRVH108           (0x06)
#define NVS_MOTOR_TPRS              (0x07)

#define VFD_INPUT_DC          ((vfd.voltInputType == 2) || (vfd.voltInputType==3) || (vfd.voltInputType==4))
#define VFD_INPUT_AC          (vfd.voltInputType == 1)
#define VFD_INPUT_NULL        (vfd.voltInputType == 0)
#define VFD_INPUT_24V         (vfd.voltInputType == 5)
#define VFD_INPUT_NULL        (vfd.voltInputType == 0)
#define VFD_INPUT_UNDEF       (vfd.voltInputType == 6)

//-------------------------------------------------------------
// 逆变定义
//-------------------------------------------------------------
#define INV_PWM_FREQ      (5000)          //逆变载波频率5KHZ
#define FREQSTART         (5)             //系统启动频率5HZ
#define FREQWORK          (50)            //系统工作频率50HZ                      (5-100hz)
#define FREQMIN           (5)             //系统最小频率5HZ
#define FREQMAX           (200)           //系统最大频率200HZ
#define FREQSTEP          (0.010)         //频率变化步长0.020HZ                   (1-15s)
#define FREQSTEP_STARTUP  (0.010)         //频率变化步长0.010HZ                   (1-15s)
#define ACVOUTWORK        (3800)          //系统输出电压380V                      (0-440v)
#define ACVOUTMAX         (4600)          //系统输出电压460V 
#define MAX_MODULATION    (1154)          //最大调节幅度                          
#define WORKTIMES         (45)            //系统工作时间45分钟
#define RESETTIMES        (10 * 1000)      //故障恢复时间10*100(1秒钟,10ms计时)
#define POWER_LOW_FLT     (700)           //输入欠压故障点70V
#define DCVOUTMAX         (6500)          //母线电压650V
#define POWER_OVER_FLT    (1400)          //输入过压故障点140V
#define POWER_OC_FLT      (470)           //输入过载故障点47A
#define MAIN_OVER_FLT     (7000)          //母线过压故障点700V
#define OUT_OC1_FLT       (65)            //输出过载1故障点6.5A
#define OUT_OC2_FLT       (85)            //输出过载2故障点8.5A
#define OUT_LOST_START    (20)            //输出电流缺相起始判断点2A
#define IGBT_TEM_FLT      (900)           //IGBT温度故障点90℃
#define RUN_FORWARD       (0x01)          //电机正转
#define RUN_REVERSE       (0x02)          //电机反转

#define VF_CURVE_F_MAX        (FREQMAX)

//VF curve : y = kx + b        380/50Hz 压频比
#define VF_DEFAULT_V1         (200.0f) // 
#define VF_DEFAULT_F1         (30.00f)

#define VF_DEFAULT_V2         (380.0f) // 
#define VF_DEFAULT_F2         (50.00f)

#define VF_DEFAULT_V3         (400.0f) // 
#define VF_DEFAULT_F3         (60.00f)

#define VF_DEFAULT_K1         (9.0f) //
#define VF_DEFAULT_B1         (-70.0f)
#define VF_DEFAULT_K2         (2.0f) // 
#define VF_DEFAULT_B2         (280.0f)

#define VF_DEFAULT_STEPUP     (3.30f) //  
#define VF_DEFAULT_STEPDOWN   (3.30f)


#define RECORD_cache_tsl_offset  (20)
typedef struct
{
    uint32_t        tsl_time;
    record_log_t    tsl_log;
}log_cache_t;

typedef struct
{
    uint8_t  clear_read     : 1;
    uint8_t  serial_read    : 1;
    uint8_t  filter_event   : 1;
    uint8_t  filter_diag    : 1;
    uint8_t  filter_time    : 1;
    
    uint8_t  filter_event_code;
    uint8_t  filter_diag_code;
    uint32_t  from;
    uint32_t  to;
}log_ctrl_t;

typedef struct {
  
    int         cur_tsl_time;
    uint8_t     cur_tsl_date[6];
    int         end_tsl_time;
    uint8_t     end_tsl_date[6];
    int         counts;
    int         acc_index;
    int         read_index;
    int         read_offset;
    int         cache_time;
    int         cache_tsl_offset;
    
    log_cache_t cache[RECORD_cache_tsl_offset];
    log_ctrl_t  ctrl;
    
    int         serial_counts;
    
    void        *ptsdb;

    uint32_t    query_counts;
    
    uint8_t     query_haved_flag  : 1;
    uint8_t     reset_flag        : 1;
    
    uint8_t     db_type;
}record_com_t;


#ifndef bool
#define bool int8_t
#endif

#ifndef TRUE
#define TRUE 1
#endif
#ifndef FALSE
#define FALSE 0
#endif

extern int vfd_io_edge_update_val(void);
extern TIM_HandleTypeDef htim5;
extern int nvsdata_read_config(void);
extern int nvsdata_read_acc(void);
extern int nvsdata_write_acc(void);
extern int nvsdata_init_acc(void);
extern int nvsdata_force_write_acc(void);
extern int nvsdata_write_config(void);
extern int nvs_datas_init(void);
extern void *ccm_malloc(size_t size);
extern void record_timeout_active(void);
extern void record_logdata_update_hookset(int (*hook)(uint8_t *buff));
extern void record_logrtc_update_hookset(int (*hook)(uint8_t *buff));
extern void record_diagdata_delaypush(uint8_t event_code,uint8_t diag_code);
extern void record_logdata_push(uint8_t event_code,uint8_t diag_code);
extern int record_pop_appendtofdb(void);
extern void record_log_unpack_printf(int time,int index, uint8_t *buf);
extern void com_cnter_deinit(com_t *ds,uint32_t time_out_set);
extern void com_cnters_init(void);
extern void com_cnter_recv(com_t *ds);
extern void com_cnter_send(com_t *ds);
extern void com_cnter_timeout_ticking(com_t *ds);
extern void com_cnters_ticking(void);
extern void ReadSetInfo(void);
extern void WriteSetInfo(void);
extern int vfd_machine_task(void);
extern void update_local_time_data(void);
extern void ParGet10MsFromNvs(void);
extern void MX_TIM8_Pfc_Init(void);
extern uint32_t SystemCoreClock;
extern int nvsdata_readFromCfgTable(char *key,uint8_t crc_enable);
extern int nvsdata_writeFromCfgTable(char *key,uint8_t crc_enable);
extern int adca_data_fix(uint16_t *data);
//-------------------------------------------------------------
// 连续型时间记录模块 日志事件码
//-------------------------------------------------------------
#define LOG_Idle            (0)             //
#define LOG_DiagAct         (1)             //
#define LOG_DiagRes         (2)             //
#define LOG_IoRising        (3)             //
#define LOG_IoFalling       (4)             //
#define LOG_FreqOn          (5)             //
#define LOG_FreqOff         (6)             //
#define LOG_SysSt           (7)             //
#define LOG_InvSt           (8)             //
#define LOG_PfcSt           (9)             //
#define LOG_ModbusOn        (10)            //
#define LOG_ModbusOff       (11)            //
#define LOG_PtuOn           (12)            //
#define LOG_PtuOff          (13)            //
#define LOG_LoadReduce      (14)            //
#define LOG_LoadResume      (15)            //
#define LOG_SetFreq         (16)            //
#define LOG_SetPvpbStart    (17)            //
#define LOG_SetPvpbStop     (18)            //
#define LOG_PowerOn         (19)            //
#define LOG_PowerOff        (20)            //
#define LOG_Inv_FW_Info     (21)            //
#define LOG_Pfc_FW_Info     (22)            //
#define LOG_Inv_FW_CrcOk    (23)            //
#define LOG_Pfc_FW_CrcOk    (24)            //
#define LOG_SetNvsCfg       (25)            //
#define LOG_SetNvsReg       (26)            //
#define LOG_SetRs485Baud    (27)            //
#define LOG_SetRTC          (28)            //
#define LOG_BoostSt         (29)            //
void Error_Handler(void);

#ifdef UAPP_MACRO
#define _EXTERN_
vfd_t         vfd = {
    .pfc = &pfc,
    .inverter = &mcsdk,
};

#else  
extern  vfd_t         vfd;
extern  uint32_t    Tim8_Freq;
extern void toolbox_dma_printf_ascii(const char *fmt, ...);
extern void toolbox_resume_rtkprintf(void);

extern void vfd_fs_set(uint8_t fs_chnn,uint8_t is_enable);

#define _EXTERN_   extern
#endif
void diag_lockstop_reset(void);
_EXTERN_ irq_t         hardware_irq_pin;

_EXTERN_ nvs_data_t    nvs_datas; /**< non-volatile storage  data */

_EXTERN_ record_com_t record_com;
_EXTERN_ com_t  com_485;
_EXTERN_ com_t  com_can;
_EXTERN_ com_t  com_ptu;
_EXTERN_ com_t  com_wifi;
_EXTERN_ com_t  com_modbus;
_EXTERN_ com_t  com_uart3_pfc;
_EXTERN_ int vfd_init(void);
_EXTERN_ int vfd_io_control(void);
_EXTERN_ const char *state_string[];
_EXTERN_ void pfc_main_entry(void *parameter);
_EXTERN_ int vfd_sub_init(void);

void TIMx_Breakin_StopAllPWM(void);
extern void Inv_SetFreqRef(float ref);

// PTU TIM8 PWM测试相关函数声明
void PTU_TIM8_PWM_Test_StateMachine(void);
void PTU_TIM8_PWM_Control(void);
void PTU_TIM8_PWM_SafeExit(void);
int vfd_start_fan(uint16_t duty, uint16_t freq);
int vfd_stop_fan(void);
void vfd_cooling_fan_logic(void);
int vfd_get_inv_breakflag(void);
int vfd_get_pfc_breakflag(void);
float hdc1080_get_temperature(void);
float hdc1080_get_moisture(void);
#endif
